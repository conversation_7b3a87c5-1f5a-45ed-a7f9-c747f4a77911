---
时间: 2025-04-14 20:36
tags:
---
以下是需要与开发人员共同制定的核心规范：

---

### **1. 元素标识规范**
- **⭐唯一性要求（必需）**：  
	- 开发需为关键 UI 元素（如按钮、输入框）添加 **唯一且稳定的标识符**（如 `data-testid="login-button"`），避免依赖易变的 CSS 选择器或 XPath。
	- 禁止使用随机生成的 ID（如 `id="id-1234"`）。
- **命名约定（建议，非必需）**：  
	- 制定一套统一的命名约定，采用 `模块名_功能名_元素类型` 的命名方式。以注册模块为例，用户名输入框可命名为 `register_username_input`，密码输入框可命名为 `register_password_input`。

---

### **2. 变更通知机制**
- **UI 变更同步**：  
	- 开发在修改 UI 结构或元素属性时，需提前通知测试团队。
	- 关键页面重构需提供 **UI 变更文档**（如元素定位方式更新说明）。
- **版本控制联动**：  
	- 代码提交时，若涉及 UI 变更，需在提交信息中标记（如 `[UI-CHANGE] 登录页按钮ID更新`）。

---

### **3. 环境与数据协作规范**
- **测试环境一致性**：  
	- 开发与测试使用 **同一套环境配置**（如相同的数据库版本、服务依赖），避免因环境差异导致测试结果不一致。
	- 开发需确保测试环境稳定性（如避免频繁重启服务导致自动化测试中断）。
- **测试数据管理**：  
	- 禁止开发在测试环境中手动修改数据，避免污染测试结果。


---

### **4. 异常处理协作**
- **错误信息标准化**：  
	- 开发需在代码中提供 **明确的错误提示**（如表单校验失败时的具体原因），便于测试脚本捕获，断言并生成精准报告。