
## 测试环境
~~地址：[登录 - 金圈企业数智管理系统](http://nt-test.buyhoo.cc/taxStatistic/#/login)~~（弃用）
地址：[登录 - 金圈企业数智管理系统](https://allscm-test.buyhoo.cc/taxStatistic/#/login)
测试环境数据库：yxl_tax_statistic
商家信息表：bus_shop


### 企业账号
- 原有测试账号：
	- 账号：xxzj
	- 密码：xxzj123456

- 民生银行测试账号：
	- 账号：minsheng
	- 密码：Qq111111
- 纳统服务费测试账号：
	- 账号：natong
	- 密码：Qq111111

### 平台账号
#### 账号 ：admin    密码：xxzj123456

## 生产环境
地址：[登录 - 金圈企业数智管理系统](https://platform.allscm.net/taxStatistic/#/home/<USER>
### 企业账号
#### 账号 ：admin_qingchun     密码：admin_qingchun
#### 账号：admin_liguan   密码：admin_liguan

#### 账号：xxzj   密码：xxzj123456
- 该账号下可用的绑定金圈的账号
	- 账号：18653935858    18610866277（小象之家八路店，便利店）
	- 账号：18988888888    888888（趁热捞，餐饮店）

### 平台账号
#### 账号 ：admin     密码：xxzj123456

## 商户角色管理
### 通过平台账号创建的角色
数据库表：sys_user内的user_type字段等于1（超级管理员），且企业账号没有删除权限
### 通过企业账号创建的角色
数据库表：sys_user内的user_type字段等于2（普通管理员），且企业账号拥有删除权限