{"nodes": [{"id": "cd9ad49d95679b3a", "type": "text", "text": "支付方式", "x": -700, "y": -140, "width": 100, "height": 60}, {"id": "6c3ac1fc774465df", "type": "text", "text": "线下支付", "x": -560, "y": 10, "width": 120, "height": 60}, {"id": "d3e8c6fb60640098", "type": "text", "text": "线上支付", "x": -560, "y": -285, "width": 120, "height": 60}, {"id": "6dfcb2b4bb526f76", "type": "text", "text": "储值卡", "x": 180, "y": 255, "width": 250, "height": 60}, {"id": "121c0d4fa30b32b5", "type": "text", "text": "微信", "x": 180, "y": -65, "width": 250, "height": 60}, {"id": "f1eba91af5ceb11b", "type": "text", "text": "支付宝", "x": 180, "y": 15, "width": 250, "height": 60}, {"id": "1ed137b72be54b8f", "type": "text", "text": "现金", "x": 180, "y": 95, "width": 250, "height": 60}, {"id": "8627eb56b0eb77a7", "type": "text", "text": "银行卡", "x": 180, "y": 175, "width": 250, "height": 60}, {"id": "ced55b8123f77d39", "type": "text", "text": "免密支付：扫码枪扫付款码", "x": -380, "y": -65, "width": 160, "height": 60}, {"id": "89271e83bd7fd23e", "type": "text", "text": "非免密支付", "x": -380, "y": 96, "width": 160, "height": 60}, {"id": "6c1a0397b489ba8b", "type": "text", "text": "刷脸付、线上小程序、H5下单支付（小程序花费钱包余额进行购买的商品不产生交易手续费）", "x": 180, "y": -420, "width": 250, "height": 108}, {"id": "b029ef58ce7c4bd5", "type": "text", "text": "支付宝、微信、云闪付、TNG、POS机支付等（扫码枪扫付款码）", "x": 180, "y": -292, "width": 250, "height": 75}, {"id": "5ffcc8d98d30e21d", "type": "text", "text": "组合付（混合支付），组合付仅收取经收单机构的金额交易手续费，现金等线下支付部分不再计算额外费用", "x": 180, "y": -197, "width": 250, "height": 117}, {"id": "376b044e9ce32443", "type": "text", "text": "有交易手续费，无服务费：\n- 交易手续费=订单金额* [[纳统金额计算逻辑#交易手续费的费率设置|结算费率]]", "x": -180, "y": -330, "width": 220, "height": 150}, {"id": "4dff85f7e1809460", "type": "text", "text": "无交易手续费，有服务费：\n- 服务费=订单金额* [[纳统金额计算逻辑#服务费的费率设置|利润率]]", "x": -180, "y": 38, "width": 220, "height": 175}], "edges": [{"id": "61a348db59fb72ea", "fromNode": "4dff85f7e1809460", "fromSide": "right", "toNode": "121c0d4fa30b32b5", "toSide": "left"}, {"id": "b4ad5dd5551cf974", "fromNode": "4dff85f7e1809460", "fromSide": "right", "toNode": "f1eba91af5ceb11b", "toSide": "left"}, {"id": "ef9258ff851176fb", "fromNode": "4dff85f7e1809460", "fromSide": "right", "toNode": "1ed137b72be54b8f", "toSide": "left"}, {"id": "385aad459db7ea3a", "fromNode": "4dff85f7e1809460", "fromSide": "right", "toNode": "8627eb56b0eb77a7", "toSide": "left"}, {"id": "e68ed1b6ed94c7a6", "fromNode": "4dff85f7e1809460", "fromSide": "right", "toNode": "6dfcb2b4bb526f76", "toSide": "left"}, {"id": "5e24cf04ed7e6045", "fromNode": "376b044e9ce32443", "fromSide": "right", "toNode": "b029ef58ce7c4bd5", "toSide": "left"}, {"id": "f4fad56401d68f1c", "fromNode": "cd9ad49d95679b3a", "fromSide": "right", "toNode": "d3e8c6fb60640098", "toSide": "left"}, {"id": "5cc10d95d6c55c9c", "fromNode": "cd9ad49d95679b3a", "fromSide": "right", "toNode": "6c3ac1fc774465df", "toSide": "left"}, {"id": "bdb27fe9d3da40ee", "fromNode": "6c3ac1fc774465df", "fromSide": "right", "toNode": "ced55b8123f77d39", "toSide": "left"}, {"id": "16c35638e953d2bc", "fromNode": "6c3ac1fc774465df", "fromSide": "right", "toNode": "89271e83bd7fd23e", "toSide": "left"}, {"id": "c15018fac64fd914", "fromNode": "d3e8c6fb60640098", "fromSide": "right", "toNode": "376b044e9ce32443", "toSide": "left"}, {"id": "00d4da6d9aa703f2", "fromNode": "376b044e9ce32443", "fromSide": "right", "toNode": "6c1a0397b489ba8b", "toSide": "left"}, {"id": "7a3582d3e52e93af", "fromNode": "89271e83bd7fd23e", "fromSide": "right", "toNode": "4dff85f7e1809460", "toSide": "left"}, {"id": "3ebdd4fcd53a38bb", "fromNode": "376b044e9ce32443", "fromSide": "right", "toNode": "5ffcc8d98d30e21d", "toSide": "left"}]}