针对线上会员设置发放的优惠券
> [!warning] 平台优惠券的bug
> 当前没有平台优惠券垫付的功能，当使用平台优惠券进行下单抵扣金额时，需线下客服进行结算金额
> 

![[优惠券-2025-03-08,11-51.png]]
## 查询条件
- 状态
	- 未过期
	- 已过期

## 新增优惠券
- 优惠券名称
- 起止时间
- 规则
- 类型
	- 仅限非折扣商品：折扣商品指参与[[商品促销]]（属秒杀商品），只要商品价格为促销价格时，均不享受该类型的优惠券。
	- 全品类：无限制，所有商品均可进行优惠
- 发放方式
	- 用户领取：用户在H5自主领取优惠券
	- 充值赠送：（逻辑不清晰）
	- 自定义赠送：（逻辑不清晰）
	- 购买产品赠送：（逻辑不清晰）
- 是否分时段
	- 是：
		- 是否分时段（shop_coupon_time表）
			- 可添加多个时段范围，只有在规定时段内可使用优惠券
		- 是否可每天使用（shop_coupon_effective表）
			- 否：可选择星期几来使用优惠券，后台根据设置的优惠券有效期，查找对应星期几的日期来启用优惠券
			- 是：则不限制星期进行使用
		- 
	- 否：不分时段，全天可用
- 是否限制发放数量
	- 是：则填写发放数量字段，填写数保存在grant_num（发放数量）字段内，填写-1为不限；每当用户领取该类型优惠券后，surplus_grant_num（剩余发放数量）字段减一，当surplus_grant_num=0时，该优惠券不再被查询，无法领取。
	- 否：则不限发放数量
- 专享优惠券
- 规则说明

## 领取记录
展示当前被领取的优惠券的线上会员信息

## 编辑
对未到生效时间的优惠券可进行编辑操作。

- 编辑按钮的展示隐藏逻辑暂不清晰

## 删除
删除当前的优惠券，不再生效

