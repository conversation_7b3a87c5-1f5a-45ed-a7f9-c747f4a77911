- 线上会员下单的金额，确认收货后资金流向平台（需在平台[[支付管理]]内配置平台管理员的支付方式）
- 该页面内统计线下收款和线上会员收款的金额，通过手动线下进行结算金额。
- 结合[[海外项目金圈1.0模块/百家商户平台Web端/优惠策略/百货豆#商家规则（也可参考 纳统服务费、成本计算逻辑.canvas 纳统服务费、成本计算逻辑 ）|百货豆]] 页面逻辑区分线上和线下收款所涉及的收款方式


## 下载对账单
- 对账单内统计日期区间内产生的除去结算手续费的订单金额（实际到账金额）
![[商家结算-2025-05-30,09-38.png]]

## 结算记录
![[商家结算-2025-05-24,10-13.png]]

## 余额结算
参考金圈1.0逻辑，
- 商家余额=线上付款（包含当日收益）+线下付款（包含当日收益）
- 线上结算金额、线下结算金额结算费率：目前写死（shop_rate表配置）
- 总结算金额=线上结算金额+线下结算金额-当日收益
	- 当日收益：查询sale_list表内pay_time和sale_list_datetime在当天0点~24点的订单记录
- 订单金额汇总计算受以下金额影响： 原始优惠券金额、配送费、积分抵扣、 支付金额、分销佣金、红包金额、退款金额、退优惠券金额
- 结算信息（线上结算金额、线下结算金额、总结算金额、实际到账金额）计算联动功能![[商家结算-2025-05-29,17-42.gif]]
> [!question] 如何确定通过线上、线下支付的订单金额是否统计到商家结算内？
> 1. 订单支付后的金额流向平台账户的，则金额统计到商家结算内；反之则直接到商家对应的账户内。
> 2. 在shop_pay_type表内添加一条shop_unique、pay_type=0的数据，表示收款到平台，若商家配置的mch_id=平台配置的mch_id，则收款到平台。
> 3. （暂未实现该功能）通过[[支付管理#编辑支付信息|支付管理内的编辑支付信息]]部分，对比平台和商家的“平台商户号”、“平台密钥”来确定订单金额是否流向平台。
> 	1. 平台和商家配置的“平台商户号”、“平台密钥”<font color="#ff0000">一致</font>，则订单金额流向平台，金额统计到商家结算内。
> 	2. 平台和商家配置的“平台商户号”、“平台密钥”<font color="#ff0000">不一致</font>，则订单金额流向商家，金额直接到账商家维护的银行卡内。

```sql
-- 更改收款到平台的配置
INSERT INTO shop_pay_type (Shop_unique, Pay_type, Mch_id, Other_set_wo) VALUES ('0', '0','1927887381835800577','wxe555c7961a0ca113');
```
