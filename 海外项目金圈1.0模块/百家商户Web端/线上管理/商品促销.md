针对H5的商品进行限时限数量促销的配置
- H5端当前只展示一个限购时间模块，多个商品的限购时间不同的情况下，H5只展示最先创建的商品限购倒计时
	- 修改方案：倒计时按照商品信息内结束时间最晚的数据展示
- H5商品限购促销数量全部售罄后，限购剩余数量接口更改成该商品的库存数量，应该在促销时间结束之前限购剩余数量均展示0
	- 修改方案：限购促销商品剩余数量=PC后台维护的促销数量
- 促销数量全部售罄后，但促销时间未结束，H5首页是否还显示促销信息
	- 修改方案：展示促销信息，但剩余数量固定为0，除非PC后台修改详情页的促销数量
- 促销数量全部售罄后，H5首页的促销商品的价格仍展示促销价格，但进入详情后恢复原价（原因是接口的限时促销可购买数量首页和详情内返回的值不同导致）
	- 修改方案：
		- 首页促销商品数量售罄或达到每人每日限购数量后价格展示原价
		- 促销商品详情页数量售罄或达到每人每日限购数量后不再展示秒杀价，默认和普通商品页面一致，显示售价、会员价、批发价等
- H5商品限购促销模块限购数量进度条显示错误，逻辑是按照   已售限购商品数量/总限购商品数量 ，当前接口未返回已售限购商品数量字段
	- 修改方案：进度条计算逻辑：已售出的促销商品数量/（已售出的促销商品数量+剩余的促销商品数量）
- 购物车内，购买参与限购促销的商品，且未达到起购量时，结算时前端未进行起购量校验，但是在付款接口内仍存在校验，起购量的限制需不需要添加限购商品的判断
	- 修改方案：起购量不受限购促销商品影响，包含限购促销商品时仍要达到起购量才可结算。