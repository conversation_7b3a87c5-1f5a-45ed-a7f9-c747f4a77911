## 页面展示
- 供货商-bind 供货商
	- 作用：绑定 APP 端的[[供货商管理]]信息（即金商云圈系统线上供货商）

## 新增商品
- **商品条码**：输入条码后光标失焦可通过接口查询云端商品或当前店铺现有商品，并带出其相关信息。
	- 仅支持输入数字，首位不能为0，且不小于7位，必填
	- 通过商品条码输入框光标失焦查询当前店铺商品时，可修改库存
		- 当通过条码查询店铺商品时修改库存>现有商品库存时，属于入库操作，入库数量=修改后库存-商品库存。
		- 当通过条码查询店铺商品时修改库存<现有商品库存时，属于出库操作，出库数量=商品库存-修改后库存。
- ⭐**进价**：后续可能改成“入库单价”、“最近入库价”
	- 必填
	- 相关更改逻辑：
		- 当店铺类型为：最近入库价
			- 小类商品的进价按照最后一次入库更改的价格为准
			- 例：对小类商品 A 进行入库，填写单价5提交后，该商品小类进价字段更改为 5；对中类商品 A1（包装换算为 10）进行入库，填写单价5提交后，该商品小类进价字段更改为 0.5（5/10=0.5）。
		- 当店铺类型为：移动加权平均
			- 进价=(原有库存存货的进价+本次进货的进价)/(原有库存存货数量+本次进货数量)
		- 编辑商品时不允许修改该字段
- **售价**：对外展示价格。包括收银机、APP、H5 线上销售平台的售价显示。
	- 必填
- **商品大类**：在[[商品分类]]内进行维护，对应一级名称。
	- 必选
- **商品小类**：在[[商品分类]]内进行维护，根据商品大类筛选出小类，对应二级名称。
	- 必选
- **供货商家**：数据展示[[供货商家]]内数据 （展示收银机线下供货商）
	- 必填
- **商品名称**：商品名，无字符输入限制。
	- 必填
- **网购价**：针对线上商城展示的价格。
	- 必填
- ⭐**库存**：商品剩余数量，可通过[[商品出入库明细]]、[[库存盘点]]、[[采购单]]、销售等多种功能模块改变。
	- 默认为 0
- **商品品牌**：自定义输入，当前未找到与之关联的功能模块。
	- 选填
- **单位**：自定义输入
	- 选填
- **规格**：自定义输入
	- 选填
- **价格同步**：开启后**售价**=**网购价**。
	- 默认关闭
- **排序**：（功能不明确）
	- 选填
- ⭐**会员价**：针对收银机线下会员和H5、小程序线上会员购买的单独设价。
	- 必填
	- 会员价<=网购价、销售价二者中最低的价格（线上H5、小程序使用的是会员价格）
- **商品简介**：商品介绍，
	- 选填，无输入限制。
- **计价类型**：
	- 按重量
		- 按重量新增的商品在收银机会展示称重标志，表示该商品按称重计价。
	- 按件
	- 必填，默认按件
- **线上起购量**：针对线上会员（小程序、H5 端）的起购量进行限制，低于该数值无法购买。
	- 必填，仅支持输入数字
- **保质期**：可根据填写的生产日期计算出到期日期，用于在[[批次管理]]内展示
	- 选填，仅支持输入数字
- **生产日期**：同上，根据保质期计算出到期日期。
	- 选填
- **货位**：在[[海外项目金圈1.0模块/百家商户Web端/商品管理/货位管理|货位管理]]内管理，最多嵌套三层子菜单。
	- 选填
- **是否设置批发价**：“否”不设置批发价和起批量（相关字段隐藏），“是”设置批发价和起批量（相关字段展示）
	- 默认为否
- **起批量**：当H5、小程序端购买的商品达到该数值时使用批发价字段
	- 选填
- **批发价**：用于H5、小程序端达到起批量后设置的价格（海外项目新增字段）
	- 选填
- **组合**：
	- 选填，最多新增两条组合商品。
	- 添加组合
		- **小类**：基本单位，填写的库存数量是该商品的总数量
		- **中类**：根据填写的包装换算值进行捆绑销售，库存仍从基本单位内减少，列表库存显示**总库存/包装换算值**
		- **大类**：根据填写的包装换算值进行捆绑销售，库存仍从基本单位内减少，列表库存显示**总库存/包装换算值**
- **资质信息**：上传三张图片
	- 选填

## 删除
- 对列表商品进行删除操作
	- 当商品已产生库存记录时不允许删除

## 详情
- 回显商品信息
- 可编辑商品信息，不可修改库存、进价，提交后更新商品信息。

## 导出 Excel
导出文件内共包含以下 10 个字段
![[商品列表-2025-02-20,16-06.png]]

## 商品导入
可批量导入商品
- 默认填写的销售价与网购价同价

## 批量修改库存
当前功能不完善，仅支持修改库存，未在修改库存后生成出入库记录等
### 查询全改
- 针对按条件筛选的商品进行修改库存操作
- 未进行条件筛选，则默认修改全部商品库存

### 本页全改
- 针对所在页的商品进行修改库存操作


## 导入图片
请上传大小不超过5MB格式为"png", "jpg", "jpeg"的文件且商品编码与文件名称一致
- 若商品没有图片，则通过文件名=商品编码进行上传图片时会更改该商品图片
- 若商品有图片，则通过文件名=商品编码进行上传图片时会覆盖该商品图片

## 导入云端商品
- 导入商品列表查询逻辑：查询平台所有店铺创建的商品信息，过滤自己店铺的商品
- 对于组合类商品，导入时应按照组合导入
	- 例：组合商品有大中小三种规格，在查询导入商品列表时可查询到三个商品信息，选中任意一种规格的商品进行导入，其他规格应一同导入
- 导入商品仅保留**商品名称**和**商品编码**，其他字段默认为空或为 0


## 负利润、零利润
- 点击**负利润**时筛选**销售价< 进货价**的商品信息
- 点击**零利润**时筛选**销售价=进货价**的商品信息

## 列表
- 周销量：统计已完成销售的商品数据（已完成销售状态：指线上商品确认收货后、收银机直接收款后纳入统计，退款商品不影响该数据）

