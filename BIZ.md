# 业务知识文档 (BIZ.md)

## 概述
本文档整理了当前系统的所有业务知识，涵盖金圈系统、纳统系统、支付系统、海外业务等核心业务模块。

---

## 1. 金圈系统 (核心商业管理系统)

### 1.1 系统架构
金圈系统是核心的商业管理平台，支持多端应用：

#### 1.1.1 金圈1.0
- **APP端**: 移动端商户管理应用，支持语音助手功能
- **收银机端**: 
  - Windows版本（新版/旧版）
  - Android版本
  - 支持多种支付方式、商品管理、会员管理

#### 1.1.2 海外项目金圈1.0
- **收银机端**: Android版，支持TNG支付、刷脸付等
- **百家商户APP端**: 移动端商户管理
- **百家商户Web端**: PC端商户后台管理
- **百家商户平台Web端**: 平台管理后台
- **百货商家H5线上会员端**: 线上购物平台

### 1.2 核心功能模块

#### 1.2.1 商品管理
- 商品分类管理（支持自定义分类）
- 商品上下架
- 批次管理
- 商品售价调价
- 批发价、划线价管理
- 商品导入/导出

#### 1.2.2 销售管理
- 收银功能（线下/线上）
- 订单管理
- 退货处理
- 组合付支持
- 刷脸付功能

#### 1.2.3 会员管理
- 线上会员管理
- 线下会员管理
- 会员钱包
- 积分管理
- 百货豆系统

#### 1.2.4 优惠策略
- 促销管理
- 捆绑促销
- 积分提成
- 积分抵扣
- 优惠券管理

#### 1.2.5 经营统计
- 营业额统计
- 商品销售统计
- 分类销量占比
- 商家结算

---

## 2. 纳统系统 (税务统计和企业管理)

### 2.1 系统概述
纳统系统负责税务统计、企业管理和财务结算，与金圈系统深度集成。

### 2.2 账号体系

#### 2.2.1 测试环境
- **地址**: https://allscm-test.buyhoo.cc/taxStatistic/#/login
- **数据库**: yxl_tax_statistic
- **企业账号**: 
  - xxzj / xxzj123456
  - minsheng / Qq111111 (民生银行测试)
  - natong / Qq111111 (纳统服务费测试)
- **平台账号**: admin / xxzj123456

#### 2.2.2 生产环境
- **地址**: https://platform.allscm.net/taxStatistic/#/home/<USER>
- **企业账号**: admin_qingchun, admin_liguan, xxzj等
- **平台账号**: admin / xxzj123456

### 2.3 核心业务逻辑

#### 2.3.1 商户认证
- 商户信息管理
- 邀请码机制
- 权限管理

#### 2.3.2 金额计算逻辑
**支付方式分类**:
- **线上支付**: 有交易手续费，无服务费
  - 交易手续费 = 订单金额 × 结算费率
- **线下支付**: 无交易手续费，有服务费
  - 服务费 = 订单金额 × 利润率

**费用计算**:
- 服务费 = 订单总金额 - 采购成本
- 企业可设置供货商是否收取服务费
- 组合付仅收取经收单机构的金额交易手续费

#### 2.3.3 财务管理
- 对账记录生成
- 应结算金额计算
- 合作商成本占比
- 交易手续费管理

---

## 3. 支付系统

### 3.1 支付中台

#### 3.1.1 环境配置
- **生产环境**: https://pay.allscm.net/payManager/mch
  - 账号: admin / Yingxiangli123
- **测试环境**: http://pay-test.buyhoo.cc/login
  - 账号: admin / admin123

#### 3.1.2 支付方式
- 微信支付
- 支付宝
- 银行卡支付
- 现金支付
- 储值卡
- TNG支付（海外）
- 云闪付
- POS机支付

### 3.2 民生银行对接

#### 3.2.1 业务角色
- **企业(市场)**: 实际收钱方，相当于销售方
- **商户**: 实际售货方，相当于供货商

#### 3.2.2 对接流程
- 纳统业务: 供货商对应金圈商户，销售方对应金圈企业
- 金圈业务: 商户对应纳统供货商，企业对应纳统销售方
- 支付中心: 进行支付流转业务

#### 3.2.3 测试环境
- **纳统账号**: minsheng / Qq111111
- **金圈测试店铺**: 多个测试账号
- **民生银行入账demo**: 子账簿入账功能

---

## 4. 海外业务

### 4.1 多币种支持
- 支持人民币、马来西亚币等多种货币
- 不同币种店铺的结算问题
- 总店分店间币种换算
- 线上会员多币种钱包管理

### 4.2 多语言支持
- 各端多语言展示
- 字段多语言配置

### 4.3 海外支付
- TNG钱包支付
- 拉卡拉国际支付
- 多币种支付方式

### 4.4 测试配合
- 海外验证码配置
- 马来方人员测试配合
- 正式环境商户配置

---

## 5. 工贸一体化商城

### 5.1 项目概述
新的业务方向，整合工业和贸易功能。

### 5.2 相关文档
- **PRD文档**: 工贸一体化综合服务平台产品PRD文档
- **金圈2.0原型**: 针对工贸一体化的金圈ALL-SCM2.0产品PRD文档
- **原型地址**: 墨刀平台，密码: Yxl2025!
- **API文档**: Apifox平台关于货币的在线商城文档

---

## 6. 展厅内容

### 6.1 收银机展示
四台功能不同的收银机，用于展示和测试：

#### 6.1.1 旧版收银机（农贸市场）
- **功能模块**: 农、供、识、直播
- **支付方式**: 扫码枪、人脸支付
- **特色功能**: 小程序下单、一刻钟配送

#### 6.1.2 收银机（农贸，连秤）
- **主要功能**: 称重、扫码付款
- **特色**: 自动带商品清单

#### 6.1.3 收银机（识别、添加临时商品）
- **AI功能**: 摄像头识别水果等物品
- **特色**: 临时商品添加、交班功能

#### 6.1.4 餐饮收银机
- **餐饮功能**: 开台、点餐、下单、结账
- **账号**: 18988888888 / 888888
- **特色**: 前后厨同步、电票功能

### 6.2 展厅大屏
- **企业数智管理数据大屏**
- **账号**: lwj / 888888

### 6.3 一刻钟到家APP
- **骑手账号**: 13054908225 / 12345678

---

## 7. 测试和开发规范

### 7.1 UI自动化测试开发规范

#### 7.1.1 元素标识规范
- 唯一性要求: 添加唯一且稳定的标识符
- 命名约定: 模块名_功能名_元素类型
- 禁止使用随机生成的ID

#### 7.1.2 变更通知机制
- UI变更同步通知
- 版本控制联动
- 提供UI变更文档

#### 7.1.3 环境与数据协作
- 测试环境一致性
- 测试数据管理
- 禁止手动修改测试数据

#### 7.1.4 异常处理协作
- 错误信息标准化
- 明确的错误提示

### 7.2 工作必需信息

#### 7.2.1 工时管理
- **钉钉工时登记**: https://alidocs.dingtalk.com/notable/share/form/...

#### 7.2.2 开发环境
- **影响力开发测试环境导航**: http://nav.allscm.top/env.html

#### 7.2.3 堡垒机
- **公司堡垒机**: http://jump.allscm.top/core/auth/login/
- **个人堡垒机**: http://117.72.97.234:8080/

#### 7.2.4 自动化测试
- **Testim平台**: https://app.testim.io/#/signin

---

## 8. 定时任务和系统维护

### 8.1 定时任务
- **任务调度中心**: https://allscm-test.buyhoo.cc/xxl-job-admin/
- **账号**: admin / yxl123456
- **纳统统计结算金额定时任务**

### 8.2 问题处理
- 生产问题先查看云效相同或类似问题
- 测试环境发包频率控制
- 数据库测试数据保护

---

## 9. 测试账号汇总

### 9.1 原金圈地址（生产）
- **地址**: https://buyhoo.cc/shop/manager/mainPage.do
- **商家账号**: 多个测试账号，支持不同支付中心
- **平台账号**: 18369679135 / syh6543211

### 9.2 原金圈地址（测试）
- **地址**: https://test170.buyhoo.cc/shop/loginMain.do
- **商家账号**: 多个测试账号，支持不同计价方式
- **平台账号**: 18369679135 / syh12345678

### 9.3 海外H5端
- **地址**: https://test-global.buyhoo.cc/buyhoo_h5/
- **测试账号**:
  - 17820250226 / Qq111111. (海外测试01)
  - 17820250310 / Qq111111. (海外用户0310)

### 9.4 海外生产环境
- **百货商家端**: https://global-shop.all-scm.com/shopWeb
  - 01020250320 / Qq123456. (马来生产零三二零，马来令吉)
- **H5线上会员端**: https://global-shop.all-scm.com/buyhoo_h5
  - 17820250329 / Qq111111.
  - 17820250325 / Qq111111. (人脸收集账号)
- **海外生产环境支付中心**: https://global-pay.all-scm.com/payWeb
  - admin / Yingxiangli123
- **海外云片短信平台**: https://www.yunpian.com/entry
  - 15969919969 / Yingxiangli123
- **Touch n go账号**: **********
- **海外发送验证码账号**: **********

### 9.5 百家平台端
- **集团后台**: https://test170.buyhoo.cc/shop/loginMain.do?roleType=1
- **海外平台**: https://test-global.buyhoo.cc/shopWeb
- **账号**: 18369679135 / syh12345678

### 9.6 海外测试环境二轮测试账号
- **百货商家PC账号**: 17820250308 / Qq123456. (临沂利群，人民币)
- **H5线上会员端**: 17820250301 / Qq111111.
- **平台账号**: 18369679135 / syh12345678

---

## 10. 业务优化和迭代

### 10.1 批发价、划线价优化
涉及PC、APP、H5、收银机等多个端的展示和逻辑优化。

### 10.2 分类优化
涉及商品分类在各个端的展示、查询、管理功能优化。

### 10.3 海外业务优化
- 多币种支持完善
- 多语言展示优化
- 支付方式扩展

---

## 11. 详细功能模块

### 11.1 百家商户Web端详细功能

#### 11.1.1 商品管理
**商品列表功能**:
- **商品条码**: 支持数字输入，首位不能为0，不小于7位，必填
- **进价**: 根据店铺类型（最近入库价/移动加权平均）自动计算
- **售价**: 对外展示价格，包括收银机、APP、H5线上销售平台
- **网购价**: 针对线上商城展示的价格
- **会员价**: 针对收银机线下会员和H5、小程序线上会员的单独定价
- **批发价**: 达到起批量后的优惠价格
- **计价类型**: 按重量/按件
- **线上起购量**: 针对线上会员的起购量限制
- **组合商品**: 支持小类、中类、大类三种规格组合

**商品分类管理**:
- 支持自定义分类导入
- 两级分类结构：一级名称（商品大类）、分类名称（商品小类）
- 分类图标：数据库读取，不可自定义
- 分类状态：启用/停用，停用时关联商品更改为"未分类"
- 分类查询功能覆盖多个模块

**库存管理**:
- 商品出入库明细
- 库存盘点
- 批次管理（需配合店铺类型=先进先出）
  - 保质期、生产日期管理
  - 入库、调拨入库操作生成批次单
  - 盘点商品未生成入库单
- 货位管理（最多嵌套三层子菜单）

#### 11.1.2 会员管理
**线上会员**:
- H5端、小程序端会员管理
- 消费记录查询
- 数据库表: platform_cus（充值表）、customer（会员表）

**线下会员**:
- 储值卡会员（cusType=1）
- 普通会员（cusType=2）
- 赊欠会员（cusType=3）
- 会员等级管理（逻辑不完善）
  - 会员等级按会员累计获取积分判断
  - 会员价开关控制：开启时使用会员价，关闭时使用会员等级打折
- 充值记录管理
- 会员续费（逻辑不清晰）
  - 针对有效期到期的会员进行续费
  - 到期会员不享有会员价，使用销售价结算
- 储值卡消费统计（逻辑不清晰）

#### 11.1.3 优惠策略
**百货豆系统**:
- 1百货豆 = 0.01元人民币
- 充值赠送规则（10元送100豆，50元送500豆等）
- 抵扣比例设置
- 提现记录管理

**积分系统**:
- 积分获取规则（功能待完善）
  - 获取优先级：积分获取规则 > 商品积分配置 > 分类小类积分配置 > 分类大类积分配置
  - 配置：获取一积分所需消费的金额
- 积分抵扣规则（逻辑不清晰，与收银机活动商品有关联）
  - 积分使用上限设置
  - 积分抵扣规则配置
- 积分提成设置（逻辑不清晰）
  - 分类提成配置：按商品数量或金额比例提成
  - 商品提成配置：针对具体商品设置
  - 积分配置生效级别：商品 > 分类小类 > 分类大类

**优惠券管理**:
- 店铺优惠券
- 全平台优惠券
- 多币种抵扣支持

#### 11.1.4 促销管理
- 商品折扣
- 商品满赠
- 单品促销
- 捆绑促销
- 限时抢购

#### 11.1.5 团长管理
- 团长管理
- 团员订单
- 佣金设置
- 佣金明细
- 拓客记录

#### 11.1.6 经营统计
- 统计信息（分类销量占比）
- 商品销售统计
- 商品销量对比
- 营业额对比
- 分店营业额统计

#### 11.1.7 系统管理
- 员工管理
- 角色管理
- 分店管理
  - 店铺设置：出入库审核开关
  - 开启时：商品出入库操作需在商品出入库明细内审核后库存值改变
  - 关闭时：商品出入库操作直接通过，仅生成已通过记录
- 业绩管理（功能待完善）
- 充值配置

#### 11.1.8 线上管理
- 商品上下架
- 店铺信息维护（功能待完善）
- 配送管理
- 骑手管理
- 首页推荐（功能待完善）
- 线上统计
- 账户管理

### 11.2 百家商户平台Web端详细功能

#### 11.2.1 支付管理
**支付配置**:
- PC使用限制：控制收银机线下支付是否可用
- APP使用限制：控制H5、小程序在线付款
- 线下费率、线上费率设置
- 平台商户号和平台密钥配置

**支付类型**:
- 通过支付中台创建平台商户号
- 配置商户详情内的商户私钥值

#### 11.2.2 经营统计
**商家结算**:
- 线上会员下单金额统计
- 线下收款和线上收款金额统计
- 手动线下结算
- 对账单下载
- 结算记录管理

#### 11.2.3 系统管理
**APP菜单管理**:
- 新增菜单（标题名称、分类、头像）
- 菜单授权（按店铺控制菜单显示）
- 菜单编辑和删除

**意见反馈**:
- 用户反馈收集和处理

**上帝视角**:
- 统计所有店铺的名称、在线状态、开机状态
- 营业额、订单量、客单价统计
- 最后订单时间、商品销量统计

### 11.3 收银机端详细功能

#### 11.3.1 Android版收银机
**收银功能**:
- 商品列表管理
- 主单处理
- 优惠处理（手动改价、会员优惠、批发优惠）
- 售价低于进价提醒
- 活动商品积分兑换

**称重功能**:
- 无码称重（外接电子秤）
- 重量以kg展示
- 称重数量同步

**支付方式**:
- 组合付（TNG支付）
- 刷脸付（扣除线上会员钱包余额）
- 现金、支付宝、微信、银行卡
- POS机支付（民生银行对接）

**设置功能**:
- 店铺信息设置
- 收银设置
- 配件设置（小票、称重、POS、副屏）
- 线上设置
- 系统设置
- 声音设置

#### 11.3.2 会员管理
- 兑换功能（积分换商品）
- 积分增减
- 会员列表（全部、赊欠、储值、普通）
- 会员详情（消费记录、退费、充值、兑换）
- 新增会员
- 等级管理

### 11.4 H5线上会员端详细功能

#### 11.4.1 购物功能
**购物车**:
- 价格展示逻辑（网购价、会员价、销售价中最低价）
- 批发价计算（达到起批量后使用）
- 限时促销价优先展示
- 商品数量管理

**订单结算**:
- 多种支付方式
- 百货豆抵扣
- 优惠券使用
- 配送方式选择

#### 11.4.2 个人中心
**钱包管理**:
- 余额查询
- 充值记录
- 消费记录
- 多币种支持

**百货豆管理**:
- 百货豆余额
- 获取记录
- 使用记录
- 抵扣规则

**订单管理**:
- 订单状态跟踪
- 订单详情查看
- 退款申请

**其他功能**:
- 地址管理
- 优惠券管理
- 推客中心
- 意见反馈
- 联系客服

---

## 12. 业务流程详解

### 12.1 商品销售流程

#### 12.1.1 线下销售流程
1. **商品扫码/选择** → 2. **数量确认** → 3. **会员识别**（可选）→ 4. **优惠计算** → 5. **支付方式选择** → 6. **支付完成** → 7. **小票打印**

#### 12.1.2 线上销售流程
1. **商品浏览** → 2. **加入购物车** → 3. **结算页面** → 4. **地址选择** → 5. **支付方式选择** → 6. **订单提交** → 7. **商家确认** → 8. **配送/自提** → 9. **确认收货**

### 12.2 会员管理流程

#### 12.2.1 线下会员注册流程
1. **基本信息录入** → 2. **会员类型选择** → 3. **储值/积分设置** → 4. **会员卡生成** → 5. **首次充值**（可选）

#### 12.2.2 线上会员注册流程
1. **手机号验证** → 2. **基本信息填写** → 3. **支付密码设置** → 4. **人脸信息录入**（可选）→ 5. **注册完成**

### 12.3 支付结算流程

#### 12.3.1 纳统结算流程
1. **订单生成** → 2. **支付方式判断** → 3. **费用计算**（交易手续费/服务费）→ 4. **资金分配** → 5. **对账记录生成** → 6. **定时结算**

#### 12.3.2 民生银行对接流程
1. **订单创建** → 2. **支付请求** → 3. **银行处理** → 4. **支付结果回调** → 5. **订单状态更新** → 6. **资金入账**

---

## 13. 数据库结构

### 13.1 主要数据表

#### 13.1.1 金圈系统
- **shop_staff**: 登录表
- **shops**: 商户信息表
- **platform_cus**: 线上会员充值表
- **customer**: 线上会员表
- **buyhoo_DB**: 主数据库

#### 13.1.2 纳统系统
- **yxl_tax_statistic**: 测试环境数据库
- **bus_shop**: 商家信息表
- **sys_user**: 用户表（user_type: 1=超级管理员，2=普通管理员）

### 13.2 数据库连接信息

#### 13.2.1 海外测试环境
**旧测试库**:
- IP: ***************
- 端口: 3307
- 用户: root
- 密码: Yxl06@@Mysql

**新测试库**:
- IP: ***************
- 端口: 3306
- 用户: userAll
- 密码: Yxl06@@Mysql

---

## 14. 接口文档

### 14.1 海外收银机接口

#### 14.1.1 TNG支付接口
```http
# 创建订单接口
POST https://global-shop.all-scm.com/shopUpdate/appPay/createSaleListUnique.do

# 请求在线支付接口（国际拉卡拉支付）
POST https://global-shop.all-scm.com/harricane/payOnline/yiTongPaySale.do

# 返回在线支付接口响应状态
GET https://global-shop.all-scm.com/harricane/payOnline/yiTongPaySaleStatus.do

# 收银支付接口
POST https://global-shop.all-scm.com/harricane/payOnline/cashierPay.do
```

### 14.2 API文档地址
- **Apifox文档**: https://apifox.com/apidoc/shared/51237fc8-044f-452a-88a3-2ba63e38599c

---

## 15. 进销存管理

### 15.1 供货商管理
**供货商家功能**:
- 新增供货商家（与APP端供货商管理数据一致）
- 商品明细查询（按供货商筛选商品）
- 连搜功能（外接扫码枪扫描商品条码）
- 本店删除（清空供货商信息）
- 销售明细统计
- 出入库明细记录

**供货商分类**:
- 分类管理
- 商品归类

### 15.2 采购管理
**采购流程**:
1. **供货商选择** → 2. **商品添加** → 3. **数量确认** → 4. **价格确认** → 5. **采购单生成** → 6. **入库操作** → 7. **成本核算**

**采购单管理**:
- 采购单创建
- 采购单审核
- 入库确认
- 成本计算

### 15.3 库存管理
**库存操作**:
- 商品入库（采购入库、调拨入库、其他入库）
- 商品出库（销售出库、调拨出库、其他出库）
- 库存盘点
- 库存预警

**库存计算方式**:
- **最近入库价**: 按最后一次入库价格计算
- **移动加权平均**: (原库存进价+本次进货进价)/(原库存数量+本次进货数量)

---

## 16. 销售订单管理

### 16.1 销售查询
**订单统计**:
- 所有销售渠道订单汇总
- 手续费收取（默认3%）
- 网络订单完成发货、确认收货后纳入统计

**商家优惠统计**:
- 收银机整单折扣金额
- 线上会员优惠券抵扣金额
- 其他促销优惠金额

### 16.2 网络订单
**订单状态管理**:
- 待发货：需要商家确认并安排发货
- 待骑手配送：已分配骑手，等待配送
- 配送异常：配送过程中出现问题
- 待收货：商品已发出，等待客户确认
- 待自提：客户选择自提方式
- 待评价：订单完成，等待客户评价
- 已完成：订单流程全部完成

**发货管理**:
- 接受订单
- 指定骑手
- 发货确认
- 配送跟踪

### 16.3 退款管理
**退款操作**:
- 通过收银机端、APP端进行退款操作
- Web端查看退款记录和管理

**退款流程**:
1. **退款申请** → 2. **商家审核** → 3. **退款确认** → 4. **资金退回** → 5. **库存恢复**

**退款类型**:
- 未发货退款
- 已发货退款
- 质量问题退款
- 其他原因退款

---

## 17. 团长分销系统

### 17.1 团长管理
**团长功能**:
- 新增团长（平台所有会员均可申请）
- 团长详情（下级团员、佣金收益、提现明细）
- 资质管理（取消资质、恢复资质）
- 订单详情（团员购买分销订单记录）

**分销机制**:
- 分享链接推广
- 团员申请加入
- 佣金计算和分配
- 提现管理

### 17.2 佣金设置
**佣金规则**:
- 按商品设置佣金比例
- 按分类设置佣金比例
- 阶梯佣金设置
- 特殊商品佣金规则

### 17.3 团员管理
**团员功能**:
- 团员列表
- 团员订单统计
- 团员消费记录
- 团员等级管理

---

## 18. 多币种业务

### 18.1 币种支持
**支持币种**:
- 人民币（CNY）
- 马来西亚令吉（MYR）
- 其他币种（可扩展）

### 18.2 多币种问题
**业务挑战**:
1. **不同币种店铺结算**: 会员在不同币种店铺购买时的支付处理
2. **总店分店币种**: 总店分店币种不同时的商品调拨金额换算
3. **线上会员钱包**: 多币种钱包展示和管理
4. **百货豆换算**: 不同币种的百货豆换算和抵扣
5. **优惠券抵扣**: 多币种优惠券的发放和使用
6. **团长分销**: 不同币种店铺的分销金额计算

### 18.3 汇率管理
**汇率设置**:
- 实时汇率获取
- 手动汇率设置
- 汇率历史记录
- 汇率变动通知

---

## 19. 海外业务特色功能

### 19.1 TNG支付
**Touch 'n Go支付**:
- 马来西亚主流电子钱包
- 扫码支付支持
- 充值功能
- 测试账号：+60169028238，PIN: 147258

### 19.2 海外验证码
**短信服务**:
- 云片短信平台
- 账号：15969919969
- 密码：Yingxiangli123
- 海外发送验证码账号：**********

### 19.3 人脸识别
**人脸支付**:
- H5端人脸信息收集
- 刷脸付功能
- 人脸信息录入流程
- 安全验证机制

---

## 20. 系统环境配置

### 20.1 收银机环境
**Windows收银机**:
- 新版Windows收银机环境地址配置
- 旧版Windows收银机环境地址配置
- 收银机文件夹存放位置

**Android收银机**:
- 环境地址配置
- 设备连接设置
- 外设配置（打印机、电子秤、POS机）

### 20.2 开发测试环境
**环境导航**:
- 影响力开发测试环境导航：http://nav.allscm.top/env.html

**堡垒机访问**:
- 公司堡垒机：http://jump.allscm.top/core/auth/login/
- 个人堡垒机：http://117.72.97.234:8080/

### 20.3 定时任务
**任务调度**:
- 任务调度中心：https://allscm-test.buyhoo.cc/xxl-job-admin/
- 账号：admin / yxl123456
- 纳统统计结算金额定时任务

---

## 21. 测试和质量保证

### 21.1 测试环境管理
**环境问题**:
- 测试环境频繁发包影响测试
- 数据库测试数据被清除
- 建议：除堵塞问题外，一天一次发包

**数据管理**:
- 禁止手动修改测试数据
- 测试数据隔离
- 数据备份和恢复

### 21.2 自动化测试
**Testim平台**:
- 地址：https://app.testim.io/#/signin
- UI自动化测试
- 回归测试
- 性能测试

### 21.3 问题处理流程
**问题处理**:
1. **问题发现** → 2. **云效查重** → 3. **问题分析** → 4. **解决方案** → 5. **测试验证** → 6. **问题关闭**

---

## 22. 业务数据统计

### 22.1 经营数据
**统计维度**:
- 按时间统计（日、周、月、年）
- 按商品统计（单品、分类、品牌）
- 按渠道统计（线上、线下、APP）
- 按会员统计（新客、老客、等级）

### 22.2 财务数据
**财务统计**:
- 营业额统计
- 成本分析
- 利润计算
- 手续费统计
- 服务费统计

### 22.3 运营数据
**运营指标**:
- 客单价
- 复购率
- 会员增长
- 商品周转率
- 库存周转率

---

## 23. 配送管理系统

### 23.1 骑手管理
**骑手功能**:
- 添加骑手信息
- 骑手资质管理
- 配送区域设置
- 骑手绩效统计

**配送服务**:
- 线上会员购买商品后的自配送服务
- 订单分配给指定骑手
- 配送状态跟踪

### 23.2 配送设置
**配送参数**:
- **配送方式**: 自配送、第三方配送
- **配送范围**: 配送覆盖区域设置
- **起送价格**: 最低起送金额
- **免配送费价格**: 免费配送门槛
- **配送预计时长**: 预估配送时间
- **自配送配送费**: 配送费用标准

### 23.3 配送流程
**配送流程**:
1. **订单生成** → 2. **骑手分配** → 3. **商品取货** → 4. **配送中** → 5. **送达确认** → 6. **订单完成**

---

## 24. 充值和营销活动

### 24.1 充值配置
**充值满赠**:
- 充值金额设置
- 赠送金额配置
- 活动时间限制
- 规则优先级（最接近充值金额的规则生效，不可叠加）

**适用范围**:
- 收银机端线下会员充值
- H5端充值（受限，需测试）

### 24.2 营销活动
**活动类型**:
- 充值满赠活动
- 消费满减活动
- 新用户注册奖励
- 节日促销活动

**活动管理**:
- 活动创建和配置
- 活动启用/停用
- 活动效果统计
- 活动规则管理

---

## 25. 民生银行对接详细流程

### 25.1 系统架构
**三方系统关系**:
- **市场（企业）**: 拥有公户，管理多个店铺
- **店铺**: 开通子账簿，客户消费金额收纳在子账簿内
- **子账簿**: 仅做记录作用，实际资金流向市场公户

### 25.2 对接流程
**初始化流程**:
1. **支付中心配置**:
   - 商户管理内添加商户，生成商户密钥
   - 银行账户内添加银行账户，填写民生银行提供的签约编码、商户号

2. **纳统系统配置**:
   - 创建企业，配置支付中心商户号、银行编号、支付中心密钥、子账簿密钥
   - 创建角色，授权角色可见菜单
   - 创建用户，使用创建的用户账号登录企业管理

3. **金圈系统配置**:
   - 通过邀请码注册金圈商户
   - 平台商户账号在支付管理页添加新注册商铺
   - 填写支付中心维护的商户主键和私钥
   - 填写纳统生成的子账簿账号

### 25.3 交易流程
**支付流程**:
1. **客户消费** → 2. **子账簿金额增加** → 3. **实际资金流向市场公户**
4. **民生银行入账demo操作** → 5. **系统生成交易记录**

**对账流程**:
1. **次日凌晨定时任务统计前一天入账金额**
2. **手动转账或定时任务转账到商户银行卡**
3. **店铺无法自主提现，需市场公户对账后转账**

### 25.4 资金流向
**资金管理**:
- 客户消费 → 子账簿记录 → 市场公户实际收款
- 市场公户 → 对账确认 → 转账到店铺银行卡
- 子账簿仅做记录，不涉及实际资金操作

---

## 26. 系统集成和数据同步

### 26.1 系统间数据同步
**金圈与纳统同步**:
- 商户注册信息同步
- 交易数据同步
- 结算数据同步
- 用户权限同步

**支付中心集成**:
- 商户信息同步
- 交易记录同步
- 支付状态同步
- 手续费计算同步

### 26.2 数据一致性
**数据校验**:
- 订单金额校验
- 库存数据校验
- 会员信息校验
- 支付状态校验

**异常处理**:
- 数据不一致处理
- 同步失败重试
- 异常数据修复
- 数据备份恢复

---

## 27. 系统监控和运维

### 27.1 系统监控
**监控指标**:
- 系统性能监控
- 交易量监控
- 错误率监控
- 响应时间监控

**告警机制**:
- 系统异常告警
- 交易异常告警
- 数据异常告警
- 性能异常告警

### 27.2 日志管理
**日志类型**:
- 系统操作日志
- 交易流水日志
- 错误异常日志
- 用户行为日志

**日志分析**:
- 问题排查
- 性能分析
- 用户行为分析
- 业务数据分析

---

## 28. 安全和权限管理

### 28.1 用户权限
**权限体系**:
- 超级管理员（user_type=1）
- 普通管理员（user_type=2）
- 店铺员工
- 普通用户

**权限控制**:
- 菜单权限
- 操作权限
- 数据权限
- 功能权限

### 28.2 数据安全
**安全措施**:
- 数据加密传输
- 敏感信息脱敏
- 访问日志记录
- 异常行为监控

**支付安全**:
- 支付密码验证
- 交易限额控制
- 风险交易识别
- 资金安全保障

---

## 29. 移动端应用

### 29.1 APP端功能
**商户APP**:
- 商品管理
- 订单处理
- 会员管理
- 数据统计
- 店铺设置
- 忘记密码功能（新商家需后台审核后可修改密码）

**收银机APP**:
- 收银功能
- 会员管理
- 商品管理
- 设置配置

### 29.2 H5端功能
**线上商城**:
- 商品浏览
- 购物车
- 订单管理
- 会员中心
- 支付功能

---

## 30. 未来发展规划

### 30.1 功能优化
**待优化功能**:
- 积分提成逻辑完善
- 积分抵扣逻辑优化
- 会员等级功能完善
- 上帝视角功能完善

### 30.2 新功能开发
**规划功能**:
- 工贸一体化商城
- 金圈2.0系统
- 更多支付方式
- 智能推荐系统

### 30.3 技术升级
**技术方向**:
- 微服务架构
- 云原生部署
- 大数据分析
- 人工智能应用

---

## 31. 补充信息和注意事项

### 31.1 功能状态说明
**逻辑不清晰的功能**:
- 积分提成功能（在销售查询内有提成字段）
- 积分抵扣功能（与收银机活动商品有关联）
- 会员等级功能（逻辑不完善）
- 会员续费功能（逻辑不清晰）
- 储值卡消费统计（逻辑不清晰）
- 上帝视角功能（统计逻辑不清晰）

**待完善的功能**:
- 积分获取规则功能
- 店铺信息维护功能
- 首页推荐功能
- 批次管理功能（需配合先进先出店铺类型）

### 31.2 重要SQL语句
**纳统商户认证相关**:
```sql
-- 更新商户账号
UPDATE shops SET Manager_account ='' WHERE Manager_account ='';
-- 更新邀请码
UPDATE shops SET invitation_code ='XXZJXSQY1' WHERE Manager_account ='';
-- 查询商户信息
SELECT * FROM shops WHERE Manager_account = ***********;
```

**线上会员余额修改**:
```sql
-- 更改会员余额
UPDATE platform_cus SET pc_balance = 100 WHERE platform_cus_id = 2;
```

**数据库表说明**:
- **shops**: 商户信息表（金圈系统）
- **bus_shop**: 商家信息表（纳统系统）
- **platform_cus**: 线上会员充值表
- **customer**: 线上会员表
- **sys_user**: 用户表（user_type: 1=超级管理员，2=普通管理员）

### 31.3 外部文档链接
**钉钉文档**:
- 语音助手指令文档
- PC商家管理后台功能清单
- PC平台管理端功能清单
- 批次管理详细需求文档

**原型文档**:
- 工贸一体化综合服务平台产品PRD文档
- 金圈ALL-SCM2.0产品PRD文档（墨刀平台，密码: Yxl2025!）

### 31.4 系统环境切换
**收银机环境地址配置**:
- 新版Windows收银机环境地址配置（图片说明）
- 旧版Windows收银机环境地址配置（图片说明）
- Windows收银机文件夹存放位置（图片说明）

### 31.5 数据库连接补充
**海外生产环境数据库**:
- 使用JumpServer访问
- 具体服务器地址需要通过堡垒机获取

---

*本文档基于当前Obsidian知识库内容整理，涵盖了系统的主要业务知识和操作规范。文档内容持续更新，反映最新的业务需求和系统功能。本文档共整理了31个主要章节，详细描述了金圈系统、纳统系统、支付系统等核心业务模块的功能、流程和配置信息，为团队提供全面的业务参考。*

*注意：文档中标注"逻辑不清晰"或"功能待完善"的部分需要进一步确认和完善，使用时请谨慎。*
