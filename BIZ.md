# 业务知识文档 (BIZ.md)

## 概述
本文档整理了当前系统的所有业务知识，涵盖金圈系统、纳统系统、支付系统、海外业务等核心业务模块。

---

## 1. 金圈系统 (核心商业管理系统)

### 1.1 系统架构
金圈系统是核心的商业管理平台，支持多端应用：

#### 1.1.1 金圈1.0
- **APP端**: 移动端商户管理应用，支持语音助手功能
- **收银机端**: 
  - Windows版本（新版/旧版）
  - Android版本
  - 支持多种支付方式、商品管理、会员管理

#### 1.1.2 海外项目金圈1.0
- **收银机端**: Android版，支持TNG支付、刷脸付等
- **百家商户APP端**: 移动端商户管理
- **百家商户Web端**: PC端商户后台管理
- **百家商户平台Web端**: 平台管理后台
- **百货商家H5线上会员端**: 线上购物平台

### 1.2 核心功能模块

#### 1.2.1 商品管理
- 商品分类管理（支持自定义分类）
- 商品上下架
- 批次管理
- 商品售价调价
- 批发价、划线价管理
- 商品导入/导出

#### 1.2.2 销售管理
- 收银功能（线下/线上）
- 订单管理
- 退货处理
- 组合付支持
- 刷脸付功能

#### 1.2.3 会员管理
- 线上会员管理
- 线下会员管理
- 会员钱包
- 积分管理
- 百货豆系统

#### 1.2.4 优惠策略
- 促销管理
- 捆绑促销
- 积分提成
- 积分抵扣
- 优惠券管理

#### 1.2.5 经营统计
- 营业额统计
- 商品销售统计
- 分类销量占比
- 商家结算

---

## 2. 纳统系统 (税务统计和企业管理)

### 2.1 系统概述
纳统系统负责税务统计、企业管理和财务结算，与金圈系统深度集成。

### 2.2 账号体系

#### 2.2.1 测试环境
- **地址**: https://allscm-test.buyhoo.cc/taxStatistic/#/login
- **数据库**: yxl_tax_statistic
- **企业账号**: 
  - xxzj / xxzj123456
  - minsheng / Qq111111 (民生银行测试)
  - natong / Qq111111 (纳统服务费测试)
- **平台账号**: admin / xxzj123456

#### 2.2.2 生产环境
- **地址**: https://platform.allscm.net/taxStatistic/#/home/<USER>
- **企业账号**: admin_qingchun, admin_liguan, xxzj等
- **平台账号**: admin / xxzj123456

### 2.3 核心业务逻辑

#### 2.3.1 商户认证
- 商户信息管理
- 邀请码机制
- 权限管理

#### 2.3.2 金额计算逻辑
**支付方式分类**:
- **线上支付**: 有交易手续费，无服务费
  - 交易手续费 = 订单金额 × 结算费率
- **线下支付**: 无交易手续费，有服务费
  - 服务费 = 订单金额 × 利润率

**费用计算**:
- 服务费 = 订单总金额 - 采购成本
- 企业可设置供货商是否收取服务费
- 组合付仅收取经收单机构的金额交易手续费

#### 2.3.3 财务管理
- 对账记录生成
- 应结算金额计算
- 合作商成本占比
- 交易手续费管理

---

## 3. 支付系统

### 3.1 支付中台

#### 3.1.1 环境配置
- **生产环境**: https://pay.allscm.net/payManager/mch
  - 账号: admin / Yingxiangli123
- **测试环境**: http://pay-test.buyhoo.cc/login
  - 账号: admin / admin123

#### 3.1.2 支付方式
- 微信支付
- 支付宝
- 银行卡支付
- 现金支付
- 储值卡
- TNG支付（海外）
- 云闪付
- POS机支付

### 3.2 民生银行对接

#### 3.2.1 业务角色
- **企业(市场)**: 实际收钱方，相当于销售方
- **商户**: 实际售货方，相当于供货商

#### 3.2.2 对接流程
- 纳统业务: 供货商对应金圈商户，销售方对应金圈企业
- 金圈业务: 商户对应纳统供货商，企业对应纳统销售方
- 支付中心: 进行支付流转业务

#### 3.2.3 测试环境
- **纳统账号**: minsheng / Qq111111
- **金圈测试店铺**: 多个测试账号
- **民生银行入账demo**: 子账簿入账功能

---

## 4. 海外业务

### 4.1 多币种支持
- 支持人民币、马来西亚币等多种货币
- 不同币种店铺的结算问题
- 总店分店间币种换算
- 线上会员多币种钱包管理

### 4.2 多语言支持
- 各端多语言展示
- 字段多语言配置

### 4.3 海外支付
- TNG钱包支付
- 拉卡拉国际支付
- 多币种支付方式

### 4.4 测试配合
- 海外验证码配置
- 马来方人员测试配合
- 正式环境商户配置

---

## 5. 工贸一体化商城

### 5.1 项目概述
新的业务方向，整合工业和贸易功能。

### 5.2 相关文档
- **PRD文档**: 工贸一体化综合服务平台产品PRD文档
- **金圈2.0原型**: 针对工贸一体化的金圈ALL-SCM2.0产品PRD文档
- **原型地址**: 墨刀平台，密码: Yxl2025!
- **API文档**: Apifox平台关于货币的在线商城文档

---

## 6. 展厅内容

### 6.1 收银机展示
四台功能不同的收银机，用于展示和测试：

#### 6.1.1 旧版收银机（农贸市场）
- **功能模块**: 农、供、识、直播
- **支付方式**: 扫码枪、人脸支付
- **特色功能**: 小程序下单、一刻钟配送

#### 6.1.2 收银机（农贸，连秤）
- **主要功能**: 称重、扫码付款
- **特色**: 自动带商品清单

#### 6.1.3 收银机（识别、添加临时商品）
- **AI功能**: 摄像头识别水果等物品
- **特色**: 临时商品添加、交班功能

#### 6.1.4 餐饮收银机
- **餐饮功能**: 开台、点餐、下单、结账
- **账号**: 18988888888 / 888888
- **特色**: 前后厨同步、电票功能

### 6.2 展厅大屏
- **企业数智管理数据大屏**
- **账号**: lwj / 888888

### 6.3 一刻钟到家APP
- **骑手账号**: 13054908225 / 12345678

---

## 7. 测试和开发规范

### 7.1 UI自动化测试开发规范

#### 7.1.1 元素标识规范
- 唯一性要求: 添加唯一且稳定的标识符
- 命名约定: 模块名_功能名_元素类型
- 禁止使用随机生成的ID

#### 7.1.2 变更通知机制
- UI变更同步通知
- 版本控制联动
- 提供UI变更文档

#### 7.1.3 环境与数据协作
- 测试环境一致性
- 测试数据管理
- 禁止手动修改测试数据

#### 7.1.4 异常处理协作
- 错误信息标准化
- 明确的错误提示

### 7.2 工作必需信息

#### 7.2.1 工时管理
- **钉钉工时登记**: https://alidocs.dingtalk.com/notable/share/form/...

#### 7.2.2 开发环境
- **影响力开发测试环境导航**: http://nav.allscm.top/env.html

#### 7.2.3 堡垒机
- **公司堡垒机**: http://jump.allscm.top/core/auth/login/
- **个人堡垒机**: http://117.72.97.234:8080/

#### 7.2.4 自动化测试
- **Testim平台**: https://app.testim.io/#/signin

---

## 8. 定时任务和系统维护

### 8.1 定时任务
- **任务调度中心**: https://allscm-test.buyhoo.cc/xxl-job-admin/
- **账号**: admin / yxl123456
- **纳统统计结算金额定时任务**

### 8.2 问题处理
- 生产问题先查看云效相同或类似问题
- 测试环境发包频率控制
- 数据库测试数据保护

---

## 9. 测试账号汇总

### 9.1 原金圈地址（生产）
- **地址**: https://buyhoo.cc/shop/manager/mainPage.do
- **商家账号**: 多个测试账号，支持不同支付中心
- **平台账号**: 18369679135 / syh6543211

### 9.2 原金圈地址（测试）
- **地址**: https://test170.buyhoo.cc/shop/loginMain.do
- **商家账号**: 多个测试账号，支持不同计价方式
- **平台账号**: 18369679135 / syh12345678

### 9.3 海外H5端
- **地址**: https://test-global.buyhoo.cc/buyhoo_h5/
- **测试账号**: 17820250226 / Qq111111.

### 9.4 百家平台端
- **集团后台**: https://test170.buyhoo.cc/shop/loginMain.do?roleType=1
- **海外平台**: https://test-global.buyhoo.cc/shopWeb
- **账号**: 18369679135 / syh12345678

---

## 10. 业务优化和迭代

### 10.1 批发价、划线价优化
涉及PC、APP、H5、收银机等多个端的展示和逻辑优化。

### 10.2 分类优化
涉及商品分类在各个端的展示、查询、管理功能优化。

### 10.3 海外业务优化
- 多币种支持完善
- 多语言展示优化
- 支付方式扩展

---

*本文档基于当前Obsidian知识库内容整理，涵盖了系统的主要业务知识和操作规范。*
