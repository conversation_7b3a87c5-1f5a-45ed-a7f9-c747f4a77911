{"nodes": [{"id": "573a25c517e651d2", "type": "text", "text": "- **店铺**进行开通**子账簿**，客户消费的金额收纳在**子账簿**内\n- **子账簿**金额只做一个记录作用", "x": -35, "y": -100, "width": 340, "height": 104}, {"id": "14ab879cfb97c8f9", "type": "text", "text": "市场（拥有一个公户）", "x": -755, "y": 24, "width": 250, "height": 60}, {"id": "03423636c30d4e5f", "type": "text", "text": "市场下有若干个**店铺**", "x": -395, "y": 24, "width": 250, "height": 60}, {"id": "18b54bd5b26989b2", "type": "text", "text": "创建用户，使用创建的用户账号登录该企业进行管理", "x": -130, "y": -820, "width": 250, "height": 60}, {"id": "2c05aa8f578bfdb9", "type": "text", "text": "在系统管理-系统设置内通过打开带有邀请码的邀请链接进行注册金圈商户", "x": 160, "y": -830, "width": 250, "height": 80}, {"id": "9b621f64662e0c05", "type": "text", "text": "注册成功后在金圈和纳统内（供货商管理）均可查看到新注册的商户信息", "x": 160, "y": -700, "width": 250, "height": 80}, {"id": "fda09d75f0a00e82", "type": "text", "text": "## 金圈 ：\n登录平台商户账号，支付管理页内添加新注册的商铺，填写支付中心维护的商户主键和支付中心私钥，填写纳统或支付中心内生成的子账簿账号。", "x": 160, "y": -580, "width": 250, "height": 200}, {"id": "be7f5013c6d91c11", "type": "text", "text": "## 纳统 ：\n编辑供货商信息进行在线创建子账簿账号，创建后会同步到金圈的店铺信息中", "x": -130, "y": -525, "width": 250, "height": 160}, {"id": "0019116a31ffe4a6", "type": "text", "text": "## 纳统 ：\n- 创建企业\n\t- 支付中心商户号：取支付中心商户管理内的商户主键\n\t- 银行编号：取支付中心银行账户内的商户主键\n\t- 支付中心密钥：取支付中心商户管理内的商户私钥\n\t- 子账簿密钥：暂时取支付中心商户管理内的商户私钥", "x": -695, "y": -1000, "width": 250, "height": 420}, {"id": "fc815bd07125cda5", "type": "text", "text": "创建角色，授权角色可见菜单", "x": -415, "y": -820, "width": 250, "height": 60}, {"id": "069729c637ff253a", "type": "text", "text": "## 支付中心 ：\n- 支付管理-商户管理内添加商户，生成商户密钥\n- 银行业务管理-银行账户内添加银行账户：选择商户管理内新增的商户信息，签约编码、商户号填写线下通过民生银行提供的数据", "x": -1020, "y": -947, "width": 250, "height": 315}, {"id": "ad832cd15830703a", "type": "text", "text": "- 通过民生银行子账簿入账demo进行入账操作，商户维护的手机号接收民生银行的入账通知。\n\t- 支付中心：生成交易记录\n\t- 金圈：生成交易记录\n\t- 纳统：订单列表生成数据", "x": -445, "y": -560, "width": 250, "height": 230}, {"id": "8ad4e82f77889b2f", "type": "text", "text": "## 纳统\n对账管理页面：次日凌晨通过定时任务统计前一天的入账金额，或通过手动转账进行转账到商户银行卡内，或通过定时任务进行转账到银行卡", "x": -755, "y": -557, "width": 250, "height": 225}], "edges": [{"id": "1c2427263debf8d1", "fromNode": "14ab879cfb97c8f9", "fromSide": "right", "toNode": "03423636c30d4e5f", "toSide": "left", "color": "3"}, {"id": "df1068969ea7a0f8", "fromNode": "03423636c30d4e5f", "fromSide": "right", "toNode": "573a25c517e651d2", "toSide": "top", "color": "3"}, {"id": "1cfe41fa3aebe9e6", "fromNode": "573a25c517e651d2", "fromSide": "left", "toNode": "14ab879cfb97c8f9", "toSide": "top", "color": "4", "label": "客户消费->子账簿金额增加，同时实际资金流向市场公户内"}, {"id": "775e8ccafaa750a2", "fromNode": "573a25c517e651d2", "fromSide": "bottom", "toNode": "14ab879cfb97c8f9", "toSide": "bottom", "fromEnd": "arrow", "color": "1", "label": "店铺无法自主提现，实际是市场公户通过子账簿进行对账，市场公户再提现把资金打到店铺银行卡"}, {"id": "5b7e83ebc889ef63", "fromNode": "0019116a31ffe4a6", "fromSide": "right", "toNode": "fc815bd07125cda5", "toSide": "left"}, {"id": "2ac03df4473775fc", "fromNode": "fc815bd07125cda5", "fromSide": "right", "toNode": "18b54bd5b26989b2", "toSide": "left"}, {"id": "c161a404d54be347", "fromNode": "18b54bd5b26989b2", "fromSide": "right", "toNode": "2c05aa8f578bfdb9", "toSide": "left"}, {"id": "43911973a6f85666", "fromNode": "2c05aa8f578bfdb9", "fromSide": "bottom", "toNode": "9b621f64662e0c05", "toSide": "top"}, {"id": "24f9d99035e04878", "fromNode": "069729c637ff253a", "fromSide": "right", "toNode": "0019116a31ffe4a6", "toSide": "left"}, {"id": "c626b80052ed2fe9", "fromNode": "9b621f64662e0c05", "fromSide": "bottom", "toNode": "fda09d75f0a00e82", "toSide": "top"}, {"id": "25e0b25803bd12bd", "fromNode": "fda09d75f0a00e82", "fromSide": "left", "toNode": "be7f5013c6d91c11", "toSide": "right"}, {"id": "1d07225a338534cf", "fromNode": "be7f5013c6d91c11", "fromSide": "left", "toNode": "ad832cd15830703a", "toSide": "right"}, {"id": "24558b2b7f60f204", "fromNode": "ad832cd15830703a", "fromSide": "left", "toNode": "8ad4e82f77889b2f", "toSide": "right"}]}