/* Define CSS variables */
:root {
  --line-number-gutter-width: 38px;
  --line-number-gutter-padding: 16px;
  --border-radius: 5px;
  --code-padding: 8px;
  --header-padding: 0px;
  --header-spacing: 15px;
  --language-icon-size: 32px;
}

/* Codeblock background colors */
.codeblock-customizer .markdown-rendered pre.codeblock-customizer-pre,
.codeblock-customizer .markdown-source-view .HyperMD-codeblock.codeblock-customizer-line {
  background-color: var(--codeblock-background-color, var(--codeblock-customizer-codeblock-background-color));
}

.markdown-source-view.mod-cm6 .code-block-flair {
  display: inline-block !important;
}

/* Hidden elements */
/*.codeblock-customizer-hidden-element {
  display: none;
}*/

/* Indentation lines */
.codeblock-customizer-indentation-guide::before {
  display: none;
}
.codeblock-customizer-show-indentation-lines .markdown-reading-view .codeblock-customizer-indentation-guide {
  position: relative;
  display: inline-block;
}
.codeblock-customizer-show-indentation-lines .markdown-reading-view .codeblock-customizer-indentation-guide::before {
  content: "\200B";
  display: block;
  width: 1px;
  border-right: var(--indentation-guide-width) solid var(--indentation-guide-color);
  color: transparent;
  position: absolute;
  top: 0;
  bottom: 0;
  transform: translateX(0.15em);
}

/* indent in edit mode */
.markdown-source-view:not(.is-readable-line-width) .indented-line {
  left: calc(var(--list-indent) * var(--level) + var(--diff, 0px)) !important;
  width: calc(100% - var(--list-indent) * var(--level));
}

/* folding in reading view */
.markdown-reading-view .codeblock-customizer-collapse-indicator,
.markdown-source-view .codeblock-customizer-collapse-indicator {
  position: relative;
  z-index: 1;
}
.markdown-reading-view .codeblock-customizer-collapse-icon,
.markdown-source-view .codeblock-customizer-collapse-icon {
  position: absolute;
  display: block;
  top: 0;
  right: 0;
  height: 100%;
  cursor: var(--cursor);
  /*padding-right: 5px;*/
  color: var(--collapse-icon-color);
  user-select: none;
}
.markdown-reading-view .codeblock-customizer-collapse-indicator .codeblock-customizer-collapse-icon,
.markdown-source-view .codeblock-customizer-collapse-indicator .codeblock-customizer-collapse-icon {
  right: -0.5em;
  padding-right: 0.5rem;
}
.markdown-reading-view .codeblock-customizer-collapse-icon,
.markdown-source-view .codeblock-customizer-collapse-icon {
  display: none;
}
.markdown-reading-view .codeblock-customizer-line-text:hover .codeblock-customizer-collapse-icon,
.markdown-source-view .codeblock-customizer-line-text:hover .codeblock-customizer-collapse-icon {
  display: block;
}
.markdown-reading-view .codeblock-customizer-line-hidden,
.markdown-source-view .codeblock-customizer-line-hidden {
  display: none !important;
}
.markdown-reading-view .codeblock-customizer-lines-below-collapsed .codeblock-customizer-collapse-icon,
.markdown-source-view .codeblock-customizer-lines-below-collapsed .codeblock-customizer-collapse-icon {
  display: block;
  color: var(--collapse-icon-color-collapsed);
}
.markdown-reading-view .codeblock-customizer-foldPlaceholder,
.markdown-source-view .codeblock-customizer-foldPlaceholder {
  color: var(--text-faint);
  background-color: transparent;
  border: none;
  margin-left: 8px;
}

/* Semi-folding */
.codeblock-customizer-use-semifold .markdown-source-view .HyperMD-codeblock-bg.codeblock-customizer-fade-out-line0 .cm-hmd-codeblock,
.codeblock-customizer-use-semifold .markdown-source-view .HyperMD-codeblock-bg.codeblock-customizer-fade-out-line0 .codeblock-customizer-line-number .codeblock-customizer-line-number-element,
.codeblock-customizer-use-semifold .markdown-source-view .HyperMD-codeblock-bg.codeblock-customizer-fade-out-line0 .codeblock-customizer-line-number-specific .codeblock-customizer-line-number-element,
.codeblock-customizer-use-semifold .markdown-rendered .codeblock-customizer-codeblock-semi-collapsed .codeblock-customizer-fade-out-line0 .codeblock-customizer-line-number .codeblock-customizer-line-number-element,
.codeblock-customizer-use-semifold .markdown-rendered .codeblock-customizer-codeblock-semi-collapsed .codeblock-customizer-fade-out-line0 .codeblock-customizer-line-number-specific .codeblock-customizer-line-number-element,
.codeblock-customizer-use-semifold .markdown-rendered .codeblock-customizer-codeblock-semi-collapsed .codeblock-customizer-fade-out-line0 .codeblock-customizer-line-text {
  opacity: 0.5;
}
.codeblock-customizer-use-semifold .markdown-source-view .HyperMD-codeblock-bg.codeblock-customizer-fade-out-line1 .cm-hmd-codeblock,
.codeblock-customizer-use-semifold .markdown-source-view .HyperMD-codeblock-bg.codeblock-customizer-fade-out-line1 .codeblock-customizer-line-number .codeblock-customizer-line-number-element,
.codeblock-customizer-use-semifold .markdown-source-view .HyperMD-codeblock-bg.codeblock-customizer-fade-out-line1 .codeblock-customizer-line-number-specific .codeblock-customizer-line-number-element,
.codeblock-customizer-use-semifold .markdown-rendered .codeblock-customizer-codeblock-semi-collapsed .codeblock-customizer-fade-out-line1 .codeblock-customizer-line-number .codeblock-customizer-line-number-element,
.codeblock-customizer-use-semifold .markdown-rendered .codeblock-customizer-codeblock-semi-collapsed .codeblock-customizer-fade-out-line1 .codeblock-customizer-line-number-specific .codeblock-customizer-line-number-element,
.codeblock-customizer-use-semifold .markdown-rendered .codeblock-customizer-codeblock-semi-collapsed .codeblock-customizer-fade-out-line1 .codeblock-customizer-line-text {
  opacity: 0.3;
}
.codeblock-customizer-use-semifold .markdown-source-view .HyperMD-codeblock-bg.codeblock-customizer-fade-out-line2 .cm-hmd-codeblock,
.codeblock-customizer-use-semifold .markdown-source-view .HyperMD-codeblock-bg.codeblock-customizer-fade-out-line2 .codeblock-customizer-line-number .codeblock-customizer-line-number-element,
.codeblock-customizer-use-semifold .markdown-source-view .HyperMD-codeblock-bg.codeblock-customizer-fade-out-line2 .codeblock-customizer-line-number-specific .codeblock-customizer-line-number-element,
.codeblock-customizer-use-semifold .markdown-rendered .codeblock-customizer-codeblock-semi-collapsed .codeblock-customizer-fade-out-line2 .codeblock-customizer-line-number .codeblock-customizer-line-number-element,
.codeblock-customizer-use-semifold .markdown-rendered .codeblock-customizer-codeblock-semi-collapsed .codeblock-customizer-fade-out-line2 .codeblock-customizer-line-number-specific .codeblock-customizer-line-number-element,
.codeblock-customizer-use-semifold .markdown-rendered .codeblock-customizer-codeblock-semi-collapsed .codeblock-customizer-fade-out-line2 .codeblock-customizer-line-text {
  opacity: 0.1;
}
.codeblock-customizer-use-semifold .markdown-source-view .HyperMD-codeblock-bg.codeblock-customizer-fade-out-line3 .cm-hmd-codeblock,
.codeblock-customizer-use-semifold .markdown-source-view .HyperMD-codeblock-bg.codeblock-customizer-fade-out-line3 .codeblock-customizer-line-number .codeblock-customizer-line-number-element,
.codeblock-customizer-use-semifold .markdown-source-view .HyperMD-codeblock-bg.codeblock-customizer-fade-out-line3 .codeblock-customizer-line-number-specific .codeblock-customizer-line-number-element,
.codeblock-customizer-use-semifold .markdown-rendered .codeblock-customizer-codeblock-semi-collapsed .codeblock-customizer-fade-out-line3 .codeblock-customizer-line-number .codeblock-customizer-line-number-element,
.codeblock-customizer-use-semifold .markdown-rendered .codeblock-customizer-codeblock-semi-collapsed .codeblock-customizer-fade-out-line3 .codeblock-customizer-line-number-specific .codeblock-customizer-line-number-element,
.codeblock-customizer-use-semifold .markdown-rendered .codeblock-customizer-codeblock-semi-collapsed .codeblock-customizer-fade-out-line3 .codeblock-customizer-line-text {
  opacity: 0.05;
}

.codeblock-customizer-use-semifold .markdown-source-view .HyperMD-codeblock-bg.codeblock-customizer-fade-out-line0,
.codeblock-customizer-use-semifold .markdown-rendered .codeblock-customizer-codeblock-semi-collapsed .codeblock-customizer-fade-out-line0 {
  background: linear-gradient(to bottom, color-mix(in srgb, var(--codeblock-background-color, var(--codeblock-customizer-codeblock-background-color)) 100%, var(--background-primary)) 0%, color-mix(in srgb, var(--codeblock-background-color, var(--codeblock-customizer-codeblock-background-color)) 75%, var(--background-primary)) 100%);
}
.codeblock-customizer-use-semifold .markdown-source-view .HyperMD-codeblock-bg.codeblock-customizer-fade-out-line0 .codeblock-customizer-line-number,
.codeblock-customizer-use-semifold .markdown-source-view .HyperMD-codeblock-bg.codeblock-customizer-fade-out-line0 .codeblock-customizer-line-number-specific,
.codeblock-customizer-use-semifold .markdown-rendered .codeblock-customizer-codeblock-semi-collapsed .codeblock-customizer-fade-out-line0 .codeblock-customizer-line-number,
.codeblock-customizer-use-semifold .markdown-rendered .codeblock-customizer-codeblock-semi-collapsed .codeblock-customizer-fade-out-line0 .codeblock-customizer-line-number-specific {
  background: linear-gradient(to bottom, color-mix(in srgb, var(--gutter-background-color, var(--codeblock-customizer-gutter-background-color)) 100%, var(--background-primary)) 0%, color-mix(in srgb, var(--gutter-background-color, var(--codeblock-customizer-gutter-background-color)) 75%, var(--background-primary)) 100%);
}

.codeblock-customizer-use-semifold .markdown-source-view .HyperMD-codeblock-bg.codeblock-customizer-fade-out-line1,
.codeblock-customizer-use-semifold .markdown-rendered .codeblock-customizer-codeblock-semi-collapsed .codeblock-customizer-fade-out-line1 {
  background: linear-gradient(to bottom, color-mix(in srgb, var(--codeblock-background-color, var(--codeblock-customizer-codeblock-background-color)) 75%, var(--background-primary)) 0%, color-mix(in srgb, var(--codeblock-background-color, var(--codeblock-customizer-codeblock-background-color)) 50%, var(--background-primary)) 100%);
}
.codeblock-customizer-use-semifold .markdown-source-view .HyperMD-codeblock-bg.codeblock-customizer-fade-out-line1 .codeblock-customizer-line-number,
.codeblock-customizer-use-semifold .markdown-source-view .HyperMD-codeblock-bg.codeblock-customizer-fade-out-line1 .codeblock-customizer-line-number-specific,
.codeblock-customizer-use-semifold .markdown-rendered .codeblock-customizer-codeblock-semi-collapsed .codeblock-customizer-fade-out-line1 .codeblock-customizer-line-number,
.codeblock-customizer-use-semifold .markdown-rendered .codeblock-customizer-codeblock-semi-collapsed .codeblock-customizer-fade-out-line1 .codeblock-customizer-line-number-specific {
  background: linear-gradient(to bottom, color-mix(in srgb, var(--gutter-background-color, var(--codeblock-customizer-gutter-background-color)) 75%, var(--background-primary)) 0%, color-mix(in srgb, var(--gutter-background-color, var(--codeblock-customizer-gutter-background-color)) 50%, var(--background-primary)) 100%);
}

.codeblock-customizer-use-semifold .markdown-source-view .HyperMD-codeblock-bg.codeblock-customizer-fade-out-line2,
.codeblock-customizer-use-semifold .markdown-rendered .codeblock-customizer-codeblock-semi-collapsed .codeblock-customizer-fade-out-line2 {
  background: linear-gradient(to bottom, color-mix(in srgb, var(--codeblock-background-color, var(--codeblock-customizer-codeblock-background-color)) 50%, var(--background-primary)) 0%, color-mix(in srgb, var(--codeblock-background-color, var(--codeblock-customizer-codeblock-background-color)) 25%, var(--background-primary)) 100%);
}
.codeblock-customizer-use-semifold .markdown-source-view .HyperMD-codeblock-bg.codeblock-customizer-fade-out-line2 .codeblock-customizer-line-number,
.codeblock-customizer-use-semifold .markdown-source-view .HyperMD-codeblock-bg.codeblock-customizer-fade-out-line2 .codeblock-customizer-line-number-specific,
.codeblock-customizer-use-semifold .markdown-rendered .codeblock-customizer-codeblock-semi-collapsed .codeblock-customizer-fade-out-line2 .codeblock-customizer-line-number,
.codeblock-customizer-use-semifold .markdown-rendered .codeblock-customizer-codeblock-semi-collapsed .codeblock-customizer-fade-out-line2 .codeblock-customizer-line-number-specific {
  background: linear-gradient(to bottom, color-mix(in srgb, var(--gutter-background-color, var(--codeblock-customizer-gutter-background-color)) 50%, var(--background-primary)) 0%, color-mix(in srgb, var(--gutter-background-color, var(--codeblock-customizer-gutter-background-color)) 25%, var(--background-primary)) 100%);
}

.codeblock-customizer-use-semifold .markdown-source-view .HyperMD-codeblock-bg.codeblock-customizer-fade-out-line3,
.codeblock-customizer-use-semifold .markdown-rendered .codeblock-customizer-codeblock-semi-collapsed .codeblock-customizer-fade-out-line3 {
  background: linear-gradient(to bottom, color-mix(in srgb, var(--codeblock-background-color, var(--codeblock-customizer-codeblock-background-color)) 25%, var(--background-primary)) 0%, color-mix(in srgb, var(--codeblock-background-color, var(--codeblock-customizer-codeblock-background-color)) 0%, var(--background-primary)) 100%);
}
.codeblock-customizer-use-semifold .markdown-source-view .HyperMD-codeblock-bg.codeblock-customizer-fade-out-line3 .codeblock-customizer-line-number,
.codeblock-customizer-use-semifold .markdown-source-view .HyperMD-codeblock-bg.codeblock-customizer-fade-out-line3 .codeblock-customizer-line-number-specific,
.codeblock-customizer-use-semifold .markdown-rendered .codeblock-customizer-codeblock-semi-collapsed .codeblock-customizer-fade-out-line3 .codeblock-customizer-line-number,
.codeblock-customizer-use-semifold .markdown-rendered .codeblock-customizer-codeblock-semi-collapsed .codeblock-customizer-fade-out-line3 .codeblock-customizer-line-number-specific {
  background: linear-gradient(to bottom, color-mix(in srgb, var(--gutter-background-color, var(--codeblock-customizer-gutter-background-color)) 25%, var(--background-primary)) 0%, color-mix(in srgb, var(--gutter-background-color, var(--codeblock-customizer-gutter-background-color)) 0%, var(--background-primary)) 100%);
}

/* Semi-fold reading view */
.codeblock-customizer-use-semifold .markdown-rendered .codeblock-customizer-codeblock-semi-collapsed .codeblock-customizer-fade-out-line-hide {
  display: none !important;
}
.codeblock-customizer-use-semifold .markdown-rendered .codeblock-customizer-codeblock-semi-collapsed > code {
  padding-bottom: 0px;
}

.codeblock-customizer-use-semifold .markdown-rendered .codeblock-customizer-codeblock-semi-collapsed .codeblock-customizer-fade-out-line2 {
  position: relative;
  /*overflow: hidden;*/
}
.codeblock-customizer-show-uncollapse-code-button .markdown-source-view .codeblock-customizer-uncollapse-code {
  position: absolute;
  right: 50%;
  transform: translateX(50%);
  /*top: 6px;*/
  z-index: 1;
  display: inline-block;
  padding: var(--size-4-1) var(--size-4-2);
  border-radius: var(--radius-s);
  font-family: var(--font-interface);
  font-size: var(--font-ui-smaller);
  color: var(--text-muted);
  cursor: var(--cursor);
}
.codeblock-customizer-use-semifold.codeblock-customizer-show-uncollapse-code-button .markdown-rendered .codeblock-customizer-codeblock-semi-collapsed .codeblock-customizer-uncollapse-code/*,
.codeblock-customizer-header-collapse-command.codeblock-customizer-use-semifold.codeblock-customizer-show-uncollapse-code-button .markdown-rendered .codeblock-customizer-codeblock-semi-collapsed .codeblock-customizer-uncollapse-code*/ {
  position: absolute;
  right: 50%;
  transform: translateX(50%);
  top: 6px;
  z-index: 1;
  display: inline-block;
  /*padding: var(--size-4-1) var(--size-4-2);*/
  padding-top: 4px;
  padding-left: 8px;
  padding-right: 8px;
  border-radius: var(--radius-s);
  font-family: var(--font-interface);
  font-size: var(--font-ui-smaller);
  color: var(--text-muted);
  cursor: var(--cursor);
  /*margin-top: calc(var(--line-height-normal) * 1em);*/
}

.codeblock-customizer-uncollapse-code,
.markdown-embed .markdown-preview-view .codeblock-customizer-uncollapse-code {
  display: none;
}
.codeblock-customizer-uncollapse-code:hover {
  background-color: var(--gutter-background-color, var(--codeblock-customizer-gutter-background-color));
  color: cyan;
}

/* Highlighting */
.codeblock-customizer-line-highlighted,
.codeblock-customizer-highlighted-text {
  background-color: var(--codeblock-customizer-codeblock-highlight-color) !important;
}

.codeblock-customizer-active-line-highlight .cm-active,
.codeblock-customizer-active-line-highlight-editor .cm-active {
  background-color: var(--codeblock-customizer-editor-active-line-color) !important;
}
.codeblock-customizer-active-line-highlight .markdown-source-view [class*="codeblock-customizer-line"].cm-active,
.codeblock-customizer-active-line-highlight-codeblock .markdown-source-view [class*="codeblock-customizer-line"].cm-active {
  background-color: var(--codeblock-active-line-color, var(--codeblock-customizer-codeblock-active-line-color)) !important;
}

/* Active gutter line number highlight */
.codeblock-customizer-gutter-active-line .cm-active [class^='codeblock-customizer-line-number'] {
  color: var(--gutter-active-linenr-color, var(--codeblock-customizer-gutter-active-linenr-color));
}

/* Inline code editor */
.codeblock-customizer.codeblock-customizer-style-inline-code .cm-s-obsidian span.cm-inline-code {
  color: var(--codeblock-customizer-inline-code-text-color);
  font-size: var(--code-size);
  background-color: var(--codeblock-customizer-inline-code-background-color);
  vertical-align: baseline;
}

/* Inline code reading view */
.codeblock-customizer.codeblock-customizer-style-inline-code .markdown-rendered :not(pre) > code {
  color: var(--codeblock-customizer-inline-code-text-color);
  font-family: var(--font-monospace);
  background-color: var(--codeblock-customizer-inline-code-background-color);
  border-radius: var(--radius-s);
  font-size: var(--code-size);
  padding: 0.1em 0.25em;
}

/* Link */
.codeblock-customizer-link,
.codeblock-customizer-link > p,
.codeblock-customizer-header-text > p{
  display: inline;
}
.codeblock-customizer-use-semifold .markdown-source-view .HyperMD-codeblock-bg.codeblock-customizer-fade-out-line0:not(.cm-hmd-codeblock) > .codeblock-customizer-link  {
  opacity: 0.5;
}
.codeblock-customizer-use-semifold .markdown-source-view .HyperMD-codeblock-bg.codeblock-customizer-fade-out-line1:not(.cm-hmd-codeblock) > .codeblock-customizer-link  {
  opacity: 0.3;
}
.codeblock-customizer-use-semifold .markdown-source-view .HyperMD-codeblock-bg.codeblock-customizer-fade-out-line2:not(.cm-hmd-codeblock) > .codeblock-customizer-link  {
  opacity: 0.1;
}
.codeblock-customizer-use-semifold .markdown-source-view .HyperMD-codeblock-bg.codeblock-customizer-fade-out-line3:not(.cm-hmd-codeblock) > .codeblock-customizer-link  {
  opacity: 0.05;
}

/* Codeblock border styling */
.codeblock-customizer.codeblock-customizer-style-codeblock-border-left .markdown-rendered .codeblock-customizer-header-container-specific[class*="codeblock-customizer-languageSpecific-"]{
  border-top-left-radius: 0px;
}
.codeblock-customizer.codeblock-customizer-style-codeblock-border-right .markdown-rendered .codeblock-customizer-header-container-specific[class*="codeblock-customizer-languageSpecific-"]{
  border-top-right-radius: 0px;
}
.codeblock-customizer.codeblock-customizer-style-codeblock-border-left .codeblock-customizer-line[style*="--codeblock-bordercolor"] .codeblock-customizer-line-number-first {
  border-top-left-radius: 0px !important;
}
.codeblock-customizer.codeblock-customizer-style-codeblock-border-right .codeblock-customizer-line[style*="--codeblock-bordercolor"] .codeblock-customizer-line-number-first {
  border-top-right-radius: 0px !important;
}
.codeblock-customizer.codeblock-customizer-style-codeblock-border-left .codeblock-customizer-line[style*="--codeblock-bordercolor"] .codeblock-customizer-line-number-last {
  border-bottom-left-radius: 0px !important;
}
.codeblock-customizer.codeblock-customizer-style-codeblock-border-right .codeblock-customizer-line[style*="--codeblock-bordercolor"] .codeblock-customizer-line-number-last {
  border-bottom-right-radius: 0px !important;
}

.codeblock-customizer.codeblock-customizer-style-codeblock-border-left .markdown-source-view .HyperMD-codeblock[class*="codeblock-customizer-line"][class*=codeblock-customizer-language-][class*="hasLangBorderColor"],
.codeblock-customizer.codeblock-customizer-style-codeblock-border-left .markdown-source-view .codeblock-customizer-header-container[class*=codeblock-customizer-language-][class*="hasLangBorderColor"],
.codeblock-customizer.codeblock-customizer-style-codeblock-border-left .markdown-source-view .codeblock-customizer-header-container-specific[class*=codeblock-customizer-language-][class*="hasLangBorderColor"],
.codeblock-customizer.codeblock-customizer-style-codeblock-border-left .markdown-rendered pre.codeblock-customizer-pre[class*=codeblock-customizer-language-][class*="hasLangBorderColor"] {
  border-left-color: var(--codeblock-bordercolor);
  border-left-width: 5px;
  border-left-style: solid;
}

.codeblock-customizer.codeblock-customizer-style-codeblock-border-right .markdown-source-view .HyperMD-codeblock[class*="codeblock-customizer-line"][class*=codeblock-customizer-language-][class*="hasLangBorderColor"],
.codeblock-customizer.codeblock-customizer-style-codeblock-border-right .markdown-source-view .codeblock-customizer-header-container[class*=codeblock-customizer-language-][class*="hasLangBorderColor"], 
.codeblock-customizer.codeblock-customizer-style-codeblock-border-right .markdown-source-view .codeblock-customizer-header-container-specific[class*=codeblock-customizer-language-][class*="hasLangBorderColor"],
.codeblock-customizer.codeblock-customizer-style-codeblock-border-right .markdown-rendered pre.codeblock-customizer-pre[class*=codeblock-customizer-language-][class*="hasLangBorderColor"] {
  border-right-color: var(--codeblock-bordercolor);
  border-right-width: 5px;
  border-right-style: solid;
}
.codeblock-customizer.codeblock-customizer-style-codeblock-border-left .markdown-source-view .hasLangBorderColor.codeblock-customizer-fade-out-line3 {
  border-bottom-left-radius: 5px;
}
.codeblock-customizer.codeblock-customizer-style-codeblock-border-right .markdown-source-view .hasLangBorderColor.codeblock-customizer-fade-out-line3 {
  border-bottom-right-radius: 5px;
}

.codeblock-customizer-header-container, 
.codeblock-customizer-header-container-specific {
  padding-bottom: 2px;
  background: linear-gradient(var(--header-line-color, var(--codeblock-customizer-header-line-color)), var(--header-line-color, var(--codeblock-customizer-header-line-color))) bottom / 100% 2px no-repeat;
  font-family: var(--font-text);
  /*border-bottom: 2px groove var(--codeblock-customizer-header-line-color);*/
}

/* remove top left and bottom left radius if language tag is displayed but icon is not */
body.codeblock-customizer-style-codeblock-border-left:not([class*="codeblock-customizer-show-langicons"]) .codeblock-customizer-header-container-specific .codeblock-customizer-header-language-tag, 
body.codeblock-customizer-style-codeblock-border-left:not(.codeblock-customizer-show-langicons-always) .codeblock-customizer-header-container .codeblock-customizer-header-language-tag, 
body.codeblock-customizer-style-codeblock-border-left .codeblock-customizer-header-container-specific:not(:has(div > img.codeblock-customizer-icon)) .codeblock-customizer-header-language-tag {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}
/* Codeblock border styling end */

/* Copy code button adjustment */
/*.codeblock-customizer-show-langicons-always .codeblock-customizer-header-container ~ .copy-code-button,
.codeblock-customizer-show-langnames-always .codeblock-customizer-header-container ~ .copy-code-button,
.codeblock-customizer-header-collapse-command .codeblock-customizer-header-container ~ .copy-code-button*/
/*.codeblock-customizer-show-langicons-always .codeblock-customizer-header-container:not([class*='codeblock-customizer-language-nolang']) ~ .codeblock-customizer-copy-code-button,
.codeblock-customizer-show-langnames-always .codeblock-customizer-header-container:not([class*='codeblock-customizer-language-nolang']) ~ .codeblock-customizer-copy-code-button,
.codeblock-customizer-header-collapse-command .codeblock-customizer-header-container:not([class*='codeblock-customizer-language-nolang']) ~ .codeblock-customizer-copy-code-button {
  margin-top: calc(var(--language-icon-size) + 6px) !important;
}*/
/*.codeblock-customizer-header-container-specific ~ .copy-code-button*/
.codeblock-customizer-header-container-specific ~ .codeblock-customizer-copy-code-button {
  margin-top: calc(var(--language-icon-size) + 6px) !important;
}

/* Line number gutter styling */
.codeblock-customizer:not(.codeblock-customizer-show-line-numbers) .codeblock-customizer-line-number,
.codeblock-customizer-line-number-hide {
  display: none;
}
.markdown-rendered .codeblock-customizer-pre > code > div:first-child > .codeblock-customizer-line-number,
.markdown-rendered .codeblock-customizer-pre > code > div:first-child > .codeblock-customizer-line-number-specific,
.markdown-rendered .codeblock-customizer-pre > code > .codeblock-customizer-lines-below-collapsed > .codeblock-customizer-line-number,
.markdown-rendered .codeblock-customizer-pre > code > .codeblock-customizer-lines-below-collapsed > .codeblock-customizer-line-number-specific { /* Maintain gutter color when padded */
  /*box-shadow: 0px calc(-1 * var(--code-padding)) var(--codeblock-customizer-gutter-background-color);*/
  box-shadow: 0px calc(-1 * var(--code-padding)) var(--gutter-background-color, var(--codeblock-customizer-gutter-background-color));
  /*box-shadow: 
  0px var(--code-padding) var(--gutter-background-color, var(--codeblock-customizer-gutter-background-color)),
  0px calc(-1 * var(--code-padding)) var(--gutter-background-color, var(--codeblock-customizer-gutter-background-color));*/
}
.markdown-rendered .codeblock-customizer-pre > code > div:last-child > .codeblock-customizer-line-number,
.markdown-rendered .codeblock-customizer-pre > code > div:last-child > .codeblock-customizer-line-number-specific,
.markdown-rendered .codeblock-customizer-pre > code > .codeblock-customizer-lines-below-collapsed > .codeblock-customizer-line-number,
.markdown-rendered .codeblock-customizer-pre > code > .codeblock-customizer-lines-below-collapsed > .codeblock-customizer-line-number-specific{ /* Maintain gutter color when padded */
  box-shadow: 0px var(--code-padding) var(--gutter-background-color, var(--codeblock-customizer-gutter-background-color));
}
.markdown-rendered .codeblock-customizer-pre > code > div:only-child > .codeblock-customizer-line-number,
.markdown-rendered .codeblock-customizer-pre > code > div:only-child > .codeblock-customizer-line-number-specific { /* Maintain gutter color when padded */
  box-shadow: 0px calc(-1 * var(--code-padding)) var(--gutter-background-color, var(--codeblock-customizer-gutter-background-color)),0px var(--code-padding) var(--gutter-background-color, var(--codeblock-customizer-gutter-background-color));
}
.codeblock-customizer-line-number,
.codeblock-customizer-line-number-specific {
  padding-top: 2px;
  padding-left: 4px;
  padding-right: 8px;
  text-align: right;
  min-width: var(--line-number-gutter-width);
  background-color: var(--gutter-background-color, var(--codeblock-customizer-gutter-background-color));
  font-size: var(--code-size);
  font-family: var(--font-monospace);
  color: var(--gutter-text-color, var(--codeblock-customizer-gutter-text-color));
  user-select: none;
  /*display:inline-block;
  height: 100%;
  position: absolute;*/
  position: sticky;
  left: 0;
  z-index: 1;
}
.markdown-source-view .codeblock-customizer-line-number,
.markdown-source-view .codeblock-customizer-line-number-specific {
  position: absolute;
  display: inline-block;
  height: 100%;
  /*width: var(--gutter-width);*/
  /*width: var(--line-number-gutter-width);*/
  /*overflow-x: auto;*/ /* causes problem with shimmering focus and minimal */
  /*direction: rtl;*/
  left: 0;
}
.markdown-source-view .codeblock-customizer-line-number {
  width: var(--line-number-gutter-width);
}

/*.codeblock-customizer-line-number-first.codeblock-customizer-line-number-specific-number,
.codeblock-customizer-line-number-last.codeblock-customizer-line-number-specific-number {
  width: var(--gutter-width);
}*/

.codeblock-customizer-line-number-specific {
  width: var(--gutter-width);
}
.codeblock-customizer .markdown-source-view .HyperMD-codeblock:has(.codeblock-customizer-line-number-specific-number) {
  --gutter-width: var(--line-number-gutter-width);
  padding-left: calc(var(--gutter-width) + var(--line-number-gutter-padding)) !important;
}

.markdown-source-view .codeblock-customizer-line-number::-webkit-scrollbar,
.markdown-source-view .codeblock-customizer-line-number-hide::-webkit-scrollbar {
  display: none;
}
.markdown-rendered .codeblock-customizer-line-number,
.markdown-rendered .codeblock-customizer-line-number-specific {
  flex-shrink: 0;
  flex-grow: 1;
}
.codeblock-customizer-active-line-highlight.codeblock-customizer-gutter-highlight .markdown-source-view [class*="codeblock-customizer-line"].cm-active .codeblock-customizer-line-number,
.codeblock-customizer-active-line-highlight.codeblock-customizer-gutter-highlight .markdown-source-view [class*="codeblock-customizer-line"].cm-active .codeblock-customizer-line-number-specific,
.codeblock-customizer-active-line-highlight-codeblock.codeblock-customizer-gutter-highlight .markdown-source-view [class*="codeblock-customizer-line"].cm-active .codeblock-customizer-line-number,
.codeblock-customizer-active-line-highlight-codeblock.codeblock-customizer-gutter-highlight .markdown-source-view [class*="codeblock-customizer-line"].cm-active .codeblock-customizer-line-number-specific,
.codeblock-customizer-gutter-highlight [class*="codeblock-customizer-line-highlighted"] .codeblock-customizer-line-number,
.codeblock-customizer-gutter-highlight [class*="codeblock-customizer-line-highlighted"] .codeblock-customizer-line-number-specific {
  background-color: rgba(0,0,0,0);
}
.codeblock-customizer.codeblock-customizer-show-line-numbers .markdown-source-view .HyperMD-codeblock:has(.codeblock-customizer-line-number),
.codeblock-customizer .markdown-source-view .HyperMD-codeblock:has(.codeblock-customizer-line-number-specific) {
  padding-left: calc(var(--line-number-gutter-width) + var(--line-number-gutter-padding));
}

/* Bracket highlight */
.markdown-source-view [class*="codeblock-customizer-line"] .codeblock-customizer-bracket-highlight-match {
  color: var(--codeblock-bracket-highlight-color-match, var(--codeblock-customizer-codeblock-bracket-highlight-color-match));
  background-color: var(--codeblock-bracket-highlight-background-color-match, var(--codeblock-customizer-codeblock-bracket-highlight-background-color-match));
}
.markdown-source-view [class*="codeblock-customizer-line"] .codeblock-customizer-bracket-highlight-nomatch {
  color: var(--codeblock-bracket-highlight-color-nomatch, var(--codeblock-customizer-codeblock-bracket-highlight-color-nomatch));
  background-color: var(--codeblock-bracket-highlight-background-color-nomatch, var(--codeblock-customizer-codeblock-bracket-highlight-background-color-nomatch));
}
.markdown-source-view .codeblock-customizer-bracket-highlight-match:not([class*="codeblock-customizer-line"] .codeblock-customizer-bracket-highlight-match),
.markdown-source-view .codeblock-customizer-bracket-highlight-nomatch:not([class*="codeblock-customizer-line"] .codeblock-customizer-bracket-highlight-nomatch) { /* dont apply to non-codeblock lines */
  background-color: transparent;
}

/* Selection highlight */
.markdown-source-view .codeblock-customizer-line .cm-selectionMatch {
  background-color: var(--codeblock-selectionmatch-highlight-color, var(--codeblock-customizer-codeblock-selectionmatch-highlight-color));
}
.markdown-source-view .cm-selectionMatch:not(.codeblock-customizer-line .cm-selectionMatch) { /* dont apply to non-codeblock lines */
  background-color: transparent !important;
}

/* code wrapping */
.markdown-rendered .codeblock-customizer-pre > code {
  white-space: var(--wrap-code) !important;
  overflow-wrap: anywhere;
}

/* button container */
.codeblock-customizer-button-container,
.markdown-source-view .codeblock-customizer-header-button-container {
  position: absolute;
  z-index: 1;
  right: 6px;
  display: flex;
  /*gap: 15px;*/
  font-family: var(--font-interface);
  font-size: var(--font-ui-smaller);
  height: calc(var(--language-icon-size) - 6px);
}
.codeblock-customizer-button-container {
  /*display: flex;*/
  top: 6px;
}
.markdown-source-view .codeblock-customizer-header-button-container {
  /*display: none;*/
  top: 2px;
}
.markdown-rendered pre:not(:hover) > .codeblock-customizer-button-container {
  display: none;
}

/*.codeblock-customizer-header-container-specific:hover .codeblock-customizer-header-button-container {*/
  /*background-color: red;*/
  /*display: flex;
}*/

/* don't show the header button container when the code block is collapsed */
.markdown-source-view .codeblock-customizer-header-container-specific:not(:has(+ .HyperMD-codeblock-begin)) .codeblock-customizer-header-button-container,
.codeblock-customizer-header-collapse-command .markdown-source-view .codeblock-customizer-header-container:not(:has(+ .HyperMD-codeblock-begin)) .codeblock-customizer-header-button-container,
.markdown-source-view .codeblock-customizer-header-container-specific:has(+ .HyperMD-codeblock-begin.semi-folded) .codeblock-customizer-header-button-container,
.codeblock-customizer-header-collapse-command .markdown-source-view .codeblock-customizer-header-container:has(+ .HyperMD-codeblock-begin.semi-folded) .codeblock-customizer-header-button-container {
  display: none;
}
/* show header button container on hover */
.markdown-source-view .codeblock-customizer-header-container-specific:not(:has(+ .HyperMD-codeblock-begin)):hover .codeblock-customizer-header-button-container,
.codeblock-customizer-header-collapse-command .markdown-source-view .codeblock-customizer-header-container:not(:has(+ .HyperMD-codeblock-begin)):hover .codeblock-customizer-header-button-container,
.markdown-source-view .codeblock-customizer-header-container-specific:has(+ .HyperMD-codeblock-begin.semi-folded):hover .codeblock-customizer-header-button-container,
.codeblock-customizer-header-collapse-command .markdown-source-view .codeblock-customizer-header-container:has(+ .HyperMD-codeblock-begin.semi-folded):hover .codeblock-customizer-header-button-container {
  display: flex;
}
/* hide select and delete code buttons for collapsed code blocks */
.markdown-source-view .codeblock-customizer-header-container-specific:not(:has(+ .HyperMD-codeblock-begin)):hover .codeblock-customizer-header-button-container .codeblock-customizer-select-code,
.codeblock-customizer-header-collapse-command .markdown-source-view .codeblock-customizer-header-container:not(:has(+ .HyperMD-codeblock-begin)):hover .codeblock-customizer-header-button-container .codeblock-customizer-select-code,
.markdown-source-view .codeblock-customizer-header-container-specific:not(:has(+ .HyperMD-codeblock-begin)):hover .codeblock-customizer-header-button-container .codeblock-customizer-delete-code,
.codeblock-customizer-header-collapse-command .markdown-source-view .codeblock-customizer-header-container:not(:has(+ .HyperMD-codeblock-begin)):hover .codeblock-customizer-header-button-container .codeblock-customizer-delete-code,
.markdown-source-view .codeblock-customizer-header-container-specific:has(+ .HyperMD-codeblock-begin.semi-folded):hover .codeblock-customizer-header-button-container .codeblock-customizer-select-code,
.codeblock-customizer-header-collapse-command .markdown-source-view .codeblock-customizer-header-container:has(+ .HyperMD-codeblock-begin.semi-folded):hover .codeblock-customizer-header-button-container .codeblock-customizer-select-code,
.markdown-source-view .codeblock-customizer-header-container-specific:has(+ .HyperMD-codeblock-begin.semi-folded):hover .codeblock-customizer-header-button-container .codeblock-customizer-delete-code,
.codeblock-customizer-header-collapse-command .markdown-source-view .codeblock-customizer-header-container:has(+ .HyperMD-codeblock-begin.semi-folded):hover .codeblock-customizer-header-button-container .codeblock-customizer-delete-code {
  display: none;
}
/* show header button container (copy code only) when enabled */
.codeblock-customizer-always-show-copy-code-button .markdown-source-view .codeblock-customizer-header-container-specific:not(:has(+ .HyperMD-codeblock-begin)) .codeblock-customizer-header-button-container,
.codeblock-customizer-header-collapse-command.codeblock-customizer-always-show-copy-code-button .markdown-source-view .codeblock-customizer-header-container:not(:has(+ .HyperMD-codeblock-begin)) .codeblock-customizer-header-button-container,
.codeblock-customizer-always-show-copy-code-button .markdown-source-view .codeblock-customizer-header-container-specific:has(+ .HyperMD-codeblock-begin.semi-folded) .codeblock-customizer-header-button-container,
.codeblock-customizer-header-collapse-command.codeblock-customizer-always-show-copy-code-button .markdown-source-view .codeblock-customizer-header-container:has(+ .HyperMD-codeblock-begin.semi-folded) .codeblock-customizer-header-button-container {
  display: flex;
}
/* hide select and delete code buttons when show (copy code only) is enabled */
.codeblock-customizer-always-show-copy-code-button .markdown-source-view .codeblock-customizer-header-container-specific:not(:has(+ .HyperMD-codeblock-begin)) .codeblock-customizer-header-button-container .codeblock-customizer-select-code,
.codeblock-customizer-header-collapse-command.codeblock-customizer-always-show-copy-code-button .markdown-source-view .codeblock-customizer-header-container:not(:has(+ .HyperMD-codeblock-begin)) .codeblock-customizer-header-button-container .codeblock-customizer-select-code,
.codeblock-customizer-always-show-copy-code-button .markdown-source-view .codeblock-customizer-header-container-specific:not(:has(+ .HyperMD-codeblock-begin)) .codeblock-customizer-header-button-container .codeblock-customizer-delete-code,
.codeblock-customizer-header-collapse-command.codeblock-customizer-always-show-copy-code-button .markdown-source-view .codeblock-customizer-header-container:not(:has(+ .HyperMD-codeblock-begin)) .codeblock-customizer-header-button-container .codeblock-customizer-delete-code,
.codeblock-customizer-always-show-copy-code-button .markdown-source-view .codeblock-customizer-header-container-specific:has(+ .HyperMD-codeblock-begin.semi-folded) .codeblock-customizer-header-button-container .codeblock-customizer-select-code,
.codeblock-customizer-header-collapse-command.codeblock-customizer-always-show-copy-code-button .markdown-source-view .codeblock-customizer-header-container:has(+ .HyperMD-codeblock-begin.semi-folded) .codeblock-customizer-header-button-container .codeblock-customizer-select-code,
.codeblock-customizer-always-show-copy-code-button .markdown-source-view .codeblock-customizer-header-container-specific:has(+ .HyperMD-codeblock-begin.semi-folded) .codeblock-customizer-header-button-container .codeblock-customizer-delete-code,
.codeblock-customizer-header-collapse-command.codeblock-customizer-always-show-copy-code-button .markdown-source-view .codeblock-customizer-header-container:has(+ .HyperMD-codeblock-begin.semi-folded) .codeblock-customizer-header-button-container .codeblock-customizer-delete-code {
  display: none;
}

/* don't display the first-line button container, when either header is specified, forced, or collapse command is used */
.markdown-source-view .codeblock-customizer-header-container-specific + .HyperMD-codeblock-begin .codeblock-customizer-button-container,
.codeblock-customizer-show-langicons-always .markdown-source-view .codeblock-customizer-header-container:not([class*='codeblock-customizer-language-nolang']) + .HyperMD-codeblock-begin .codeblock-customizer-button-container,
.codeblock-customizer-show-langnames-always .markdown-source-view .codeblock-customizer-header-container:not([class*='codeblock-customizer-language-nolang']) + .HyperMD-codeblock-begin .codeblock-customizer-button-container,
.codeblock-customizer-header-collapse-command .markdown-source-view .codeblock-customizer-header-container + .HyperMD-codeblock-begin .codeblock-customizer-button-container {
  display: none;
}

/* shift button container to the left when collapse icon is displayed on the right side */
.codeblock-customizer-collapseIconRight .markdown-source-view .codeblock-customizer-header-container-specific:not(.noCollapseIcon) + .HyperMD-codeblock-begin .codeblock-customizer-button-container,
.codeblock-customizer-show-langicons-always.codeblock-customizer-collapseIconRight .markdown-source-view .codeblock-customizer-header-container:not([class*='codeblock-customizer-language-nolang']):not(.noCollapseIcon) + .HyperMD-codeblock-begin .codeblock-customizer-button-container,
.codeblock-customizer-show-langnames-always.codeblock-customizer-collapseIconRight .markdown-source-view .codeblock-customizer-header-container:not([class*='codeblock-customizer-language-nolang']):not(.noCollapseIcon) + .HyperMD-codeblock-begin .codeblock-customizer-button-container,
.codeblock-customizer-header-collapse-command.codeblock-customizer-collapseIconRight .markdown-source-view .codeblock-customizer-header-container:not(.noCollapseIcon) + .HyperMD-codeblock-begin .codeblock-customizer-button-container,
.codeblock-customizer-collapseIconRight .markdown-source-view .codeblock-customizer-header-container-specific:not(.noCollapseIcon) .codeblock-customizer-header-button-container,
.codeblock-customizer-collapseIconRight .markdown-source-view .codeblock-customizer-header-container:not(.noCollapseIcon) .codeblock-customizer-header-button-container {
  right: 38px;
}
.codeblock-customizer-collapseIconRight .markdown-rendered .codeblock-customizer-pre:has(.codeblock-customizer-header-container-specific:not(.noCollapseIcon)) .codeblock-customizer-button-container,
.codeblock-customizer-collapseIconRight.codeblock-customizer-show-langicons-always .markdown-rendered .codeblock-customizer-header-container:not([class*='codeblock-customizer-language-nolang']):not(.noCollapseIcon) ~ .codeblock-customizer-button-container,
.codeblock-customizer-collapseIconRight.codeblock-customizer-show-langnames-always .markdown-rendered .codeblock-customizer-header-container:not([class*='codeblock-customizer-language-nolang']):not(.noCollapseIcon) ~ .codeblock-customizer-button-container,
.codeblock-customizer-collapseIconRight.codeblock-customizer-header-collapse-command .markdown-rendered .codeblock-customizer-header-container:not(.noCollapseIcon) ~ .codeblock-customizer-button-container {
  right: 38px;
}

/* hide wrap code button for collapsed code blocks in reading view*/
.markdown-rendered .codeblock-customizer-pre.codeblock-customizer-codeblock-collapsed .codeblock-customizer-button-container .codeblock-customizer-wrap-code,
.markdown-rendered .codeblock-customizer-pre.codeblock-customizer-codeblock-semi-collapsed .codeblock-customizer-button-container .codeblock-customizer-wrap-code {
  display: none;
}
/* show button container (copy code only) when enabled */
.codeblock-customizer-always-show-copy-code-button .markdown-rendered .codeblock-customizer-pre.codeblock-customizer-codeblock-collapsed .codeblock-customizer-button-container,
.codeblock-customizer-always-show-copy-code-button .markdown-rendered .codeblock-customizer-pre.codeblock-customizer-codeblock-semi-collapsed .codeblock-customizer-button-container,
.codeblock-customizer-always-show-copy-code-button .markdown-rendered .codeblock-customizer-pre:has(:not(.codeblock-customizer-codeblock-collapsed)) .codeblock-customizer-button-container,
.codeblock-customizer-always-show-copy-code-button .markdown-rendered .codeblock-customizer-pre:has(:not(.codeblock-customizer-codeblock-semi-collapsed)) .codeblock-customizer-button-container {
  display: flex;
}

/* move button-container to the header */
/*.markdown-source-view .codeblock-customizer-header-container-specific + .HyperMD-codeblock-begin .codeblock-customizer-button-container,
.codeblock-customizer-header-collapse-command .markdown-source-view .codeblock-customizer-header-container + .HyperMD-codeblock-begin .codeblock-customizer-button-container,
.codeblock-customizer-header-collapse-command .markdown-source-view .codeblock-customizer-header-container-specific + .HyperMD-codeblock-begin .codeblock-customizer-button-container,
.codeblock-customizer-show-langicons-always .markdown-source-view .codeblock-customizer-header-container:not([class*='codeblock-customizer-language-nolang']) + .HyperMD-codeblock-begin .codeblock-customizer-button-container,
.codeblock-customizer-show-langnames-always .markdown-source-view .codeblock-customizer-header-container:not([class*='codeblock-customizer-language-nolang']) + .HyperMD-codeblock-begin .codeblock-customizer-button-container,
.codeblock-customizer-header-collapse-command .markdown-source-view .codeblock-customizer-header-container:not([class*='codeblock-customizer-language-nolang']) + .HyperMD-codeblock-begin .codeblock-customizer-button-container {
  top: -30px;
}*/
.markdown-rendered .codeblock-customizer-pre:has(.codeblock-customizer-header-container-specific) .codeblock-customizer-button-container,
.codeblock-customizer-show-langicons-always .markdown-rendered .codeblock-customizer-pre:has(.codeblock-customizer-header-container:not([class*='codeblock-customizer-language-nolang'])) .codeblock-customizer-button-container,
.codeblock-customizer-show-langnames-always .markdown-rendered .codeblock-customizer-pre:has(.codeblock-customizer-header-container:not([class*='codeblock-customizer-language-nolang'])) .codeblock-customizer-button-container,
.codeblock-customizer-header-collapse-command .markdown-rendered .codeblock-customizer-pre:has(.codeblock-customizer-header-container:not([class*='codeblock-customizer-language-nolang'])) .codeblock-customizer-button-container,
.codeblock-customizer-header-collapse-command .markdown-rendered .codeblock-customizer-pre:has(.codeblock-customizer-header-container-specific) .codeblock-customizer-button-container,
.codeblock-customizer-header-collapse-command .markdown-rendered .codeblock-customizer-pre:has(.codeblock-customizer-header-container) .codeblock-customizer-button-container {
  top: 0;
}

/* select code styling */
.markdown-source-view .codeblock-customizer-button-container .codeblock-customizer-select-code,
.markdown-source-view .codeblock-customizer-header-button-container .codeblock-customizer-select-code {
  padding: var(--size-4-1) var(--size-4-2);
  border-radius: var(--radius-s);
  color: var(--text-muted);
  cursor: var(--cursor);
}
.markdown-source-view .codeblock-customizer-button-container .codeblock-customizer-select-code:hover,
.markdown-source-view .codeblock-customizer-header-button-container .codeblock-customizer-select-code:hover {
  background-color: rgba(var(--mono-rgb-100), 0.075);
}

/* delete code styling */
.markdown-source-view .codeblock-customizer-button-container .codeblock-customizer-delete-code,
.markdown-source-view .codeblock-customizer-header-button-container .codeblock-customizer-delete-code {
  padding: var(--size-4-1) var(--size-4-2);
  border-radius: var(--radius-s);
  color: var(--text-muted);
  cursor: var(--cursor);
}
.markdown-source-view .codeblock-customizer-button-container .codeblock-customizer-delete-code:hover,
.markdown-source-view .codeblock-customizer-header-button-container .codeblock-customizer-delete-code:hover {
  background-color: var(--color-red);
  color: white;
}

/* wrap code styling */
.markdown-rendered .codeblock-customizer-wrap-code {
  padding: var(--size-4-1) var(--size-4-2);
  background-color: transparent;
  box-shadow: none;
  color: var(--text-muted);
  font-size: var(--font-ui-smaller);
  font-family: var(--font-interface);
}
.markdown-rendered .codeblock-customizer-wrap-code:hover {
  background-color: rgba(var(--mono-rgb-100), 0.075);
}
.codeblock-customizer-wrap-code {
  display: none;
}
.codeblock-customizer-show-wrap-code-button .codeblock-customizer-wrap-code {
  display: flex;
}

/* copy code styling (edit mode) */
.markdown-source-view .codeblock-customizer-button-container .codeblock-customizer-copy-code,
.markdown-source-view .codeblock-customizer-header-button-container .codeblock-customizer-copy-code {
  padding: var(--size-4-1) var(--size-4-2);
  border-radius: var(--radius-s);
  color: var(--text-muted);
  cursor: var(--cursor);
}
.markdown-source-view .codeblock-customizer-button-container .codeblock-customizer-copy-code:hover,
.markdown-source-view .codeblock-customizer-header-button-container .codeblock-customizer-copy-code:hover {
  background-color: rgba(var(--mono-rgb-100), 0.075);
}
/* completely disable original copy code button in edit mode */
.markdown-source-view.mod-cm6 .codeblock-customizer-line .code-block-flair {
  display: none !important;
}

/* copy code button styling (reading view) */ 
.markdown-rendered button.codeblock-customizer-copy-code-button {
  padding: var(--size-4-1) var(--size-4-2);
  background-color: transparent;
  box-shadow: none;
  color: var(--text-muted);
  font-size: var(--font-ui-smaller);
  font-family: var(--font-interface);
}
.markdown-rendered .codeblock-customizer-copy-code-button:hover {
  background-color: rgba(var(--mono-rgb-100), 0.075);
}

/* completely disable original copy code button in reading mode */
.markdown-rendered .codeblock-customizer-pre .copy-code-button {
  display: none;
}

/* Header styling */
body:not([class*='codeblock-customizer-show-langicons']) .codeblock-customizer-header-container-specific div:has(> img.codeblock-customizer-icon),
body:not(.codeblock-customizer-show-langicons-always) .codeblock-customizer-header-container div:has(> img.codeblock-customizer-icon) {
  display: none;
}
body:not([class*='codeblock-customizer-show-langnames']) .codeblock-customizer-header-container-specific .codeblock-customizer-header-language-tag,
body:not(.codeblock-customizer-show-langnames-always) .codeblock-customizer-header-container .codeblock-customizer-header-language-tag {
  display: none;
}

body:not(.codeblock-customizer-show-langicons-always):not(.codeblock-customizer-show-langnames-always):not(.codeblock-customizer-header-collapse-command) .codeblock-customizer-header-container,
body:not(.codeblock-customizer-show-langnames-always):not(.codeblock-customizer-header-collapse-command) .codeblock-customizer-header-container:not(:has(img.codeblock-customizer-icon)) {
  display: none !important;
}

body:not(.codeblock-customizer-header-collapse-command) .codeblock-customizer-header-container:not(:has(img.codeblock-customizer-icon)) {
  display: none !important;
}

.codeblock-customizer-header-container,
.codeblock-customizer-header-container-specific {
  user-select: none;
  height: var(--language-icon-size);
  padding-top: var(--header-padding);
  border-top-left-radius: var(--border-radius);
  border-top-right-radius: var(--border-radius);
  background-color: var(--header-background-color, var(--codeblock-customizer-header-background-color));
  /*border-bottom: 2px groove var(--codeblock-customizer-header-line-color);*/
  /*font-size: 14px;*/
  font-size: var(--code-size);
  display: flex !important;
  overflow: visible;
  position: relative;
}

/* changes begin */
.codeblock-customizer pre.codeblock-customizer-pre {
  border-radius: var(--border-radius) !important;
  padding: 0px !important;
  min-height: unset;
}
/* changes end */

.codeblock-customizer-header-collapse {
  position: absolute;
  color: var(--text-muted); /*var(--header-text-color, var(--codeblock-customizer-header-text-color));*/
  top: 50%;
  font-size: large;
  font-weight: bold;
  padding-top: 0.3em;
  /*width: 20px;*/
}
.codeblock-customizer-collapseIconRight .codeblock-customizer-header-collapse  {
  /* display the collapse icon on the right side */
  right: 15px;
  transform: translate(0, -50%);
}
.codeblock-customizer-collapseIconMiddle .codeblock-customizer-header-collapse {
  /* display the collapse icon centered */
  left: 50%;  
  transform: translate(-50%, -50%);
}
.codeblock-customizer-collapseIconNone .codeblock-customizer-header-collapse {
  display: none
}

.markdown-source-view .codeblock-customizer-header-container:not(:has(+ .HyperMD-codeblock-begin)),
.markdown-source-view .codeblock-customizer-header-container-specific:not(:has(+ .HyperMD-codeblock-begin)),
.markdown-rendered .codeblock-customizer-header-container:has( + .codeblock-customizer-pre.codeblock-customizer-codeblock-collapsed),
.markdown-rendered .codeblock-customizer-header-container-specific:has( + .codeblock-customizer-pre.codeblock-customizer-codeblock-collapsed) {
/*  padding-bottom: var(--header-padding);*/
  border-bottom-left-radius: var(--border-radius);
  border-bottom-right-radius: var(--border-radius);
  /*border-bottom: none;*/ /* must be removed, because it causes the icon to expand and shrink vertically on collapse/uncollapse. */
}
/*.markdown-source-view .codeblock-customizer-header-container,
.markdown-source-view .codeblock-customizer-header-container-specific { */ /* Fix for incorrectly displaying header when using "minimal" theme */ /* causes problem with things */
/*  width: calc(var(--line-width-adaptive) - var(--folding-offset)); 
  margin-left: max(calc(50% + var(--folding-offset) - var(--line-width-adaptive)/2), calc(50% + var(--folding-offset) - var(--max-width)/2)) !important;
}*/
.codeblock-customizer-header-container .codeblock-customizer-header-language-tag,
.codeblock-customizer-header-container-specific .codeblock-customizer-header-language-tag {
  height: 100%;
  padding-left: var(--header-spacing);
  padding-right: var(--header-spacing);
  margin-right: var(--header-spacing);
  padding-top: calc(1em * 0.3);
  padding-bottom: calc(1em * 0.3);
  /*font-size: inherit;*/
  font-size: 14px;
  border-radius: var(--border-radius) var(--border-radius) 0px 0px;
  background-color: var(--header-language-tag-background-color, var(--codeblock-customizer-header-language-tag-background-color));
  color: var(--header-language-tag-text-color, var(--codeblock-customizer-header-language-tag-text-color));
  font-weight: var(--codeblock-customizer-language-tag-text-bold, normal);
  font-style: var(--codeblock-customizer-language-tag-text-italic, normal);
  display: flex;
  justify-content: center;
  flex-direction: column;
}
.markdown-source-view .codeblock-customizer-header-container:not(:has(+ .HyperMD-codeblock-begin)) .codeblock-customizer-header-language-tag,
.markdown-source-view .codeblock-customizer-header-container-specific:not(:has(+ .HyperMD-codeblock-begin)) .codeblock-customizer-header-language-tag,
.markdown-rendered .codeblock-customizer-header-container:has( + .codeblock-customizer-pre.codeblock-customizer-codeblock-collapsed) .codeblock-customizer-header-language-tag,
.markdown-rendered .codeblock-customizer-header-container-specific:has( + .codeblock-customizer-pre.codeblock-customizer-codeblock-collapsed) .codeblock-customizer-header-language-tag {
  border-radius: var(--border-radius);
}
.codeblock-customizer-header-text,
.codeblock-customizer-header-text > p {
  display: flex;
  justify-content: center;
  flex-direction: column;
  padding-left: 0px;
  padding-top: calc(1em * 0.3);
  padding-bottom: calc(1em * 0.3);
  /*font-size: inherit;*/
  font-size: 14px;
  color: var(--header-text-color, var(--codeblock-customizer-header-text-color));
  font-weight: var(--codeblock-customizer-header-text-bold, normal);
  font-style: var(--codeblock-customizer-header-text-italic, normal);
}
.codeblock-customizer-header-text > p {
  display: inline;
  margin: 0;
}
/*body:not([class*='codeblock-customizer-show-langicons']):not([class*='codeblock-customizer-show-langnames']) .codeblock-customizer-header-container-specific .codeblock-customizer-header-text,
body:not(.codeblock-customizer-show-langicons-always):not(.codeblock-customizer-header-container) .codeblock-customizer-header-container .codeblock-customizer-header-text,
body .codeblock-customizer-header-container-specific:not(:has( [class^="codeblock-customizer-header-language-tag-"])) .codeblock-customizer-header-text */
.codeblock-customizer-header-container .codeblock-customizer-header-text,
.codeblock-customizer-header-container-specific .codeblock-customizer-header-text {
  padding-left: var(--header-spacing);
}
body:not(:not([class*='codeblock-customizer-show-langnames'])) .codeblock-customizer-header-container-specific .codeblock-customizer-header-language-tag + .codeblock-customizer-header-text,
body:not(:not([class*='codeblock-customizer-show-langicons'])) .codeblock-customizer-header-container-specific:has(.codeblock-customizer-icon) .codeblock-customizer-header-text,
body:not(:not([class*='codeblock-customizer-header-collapse-command'])):not(:not([class*='codeblock-customizer-show-langnames-always'])) .codeblock-customizer-header-container .codeblock-customizer-header-language-tag + .codeblock-customizer-header-text,
body:not(:not([class*='codeblock-customizer-header-collapse-command'])):not(:not([class*='codeblock-customizer-show-langicons-always'])) .codeblock-customizer-header-container:has(.codeblock-customizer-icon) .codeblock-customizer-header-text
.codeblock-customizer-show-langicons-always .codeblock-customizer-header-container:not([class*='codeblock-customizer-language-nolang']) .codeblock-customizer-header-text,
.codeblock-customizer-show-langnames-always .codeblock-customizer-header-container:not([class*='codeblock-customizer-language-nolang']) .codeblock-customizer-header-text,
body:not(:not([class*='codeblock-customizer-show-langicons-always'])).codeblock-customizer-header-collapse-command .codeblock-customizer-header-container:not([class*='codeblock-customizer-language-nolang']) .codeblock-customizer-header-text,
body:not(:not([class*='codeblock-customizer-show-langnames-always'])).codeblock-customizer-header-collapse-command .codeblock-customizer-header-container:not([class*='codeblock-customizer-language-nolang']) .codeblock-customizer-header-text {
  padding-left: 0px;
}
div:has(> img.codeblock-customizer-icon) {
  display: inline-flex;
  padding-left: var(--header-spacing);
  padding-right: var(--header-spacing);
}
.codeblock-customizer-icon {
  margin: 0 !important;
  border: none !important;
}

/* collapsed embedded code */
.markdown-embed .markdown-preview-view .markdown-preview-section:has(.codeblock-customizer-pre-parent):has(.codeblock-customizer-codeblock-collapsed),
.markdown-embed .markdown-preview-view .markdown-preview-section:has(.codeblock-customizer-pre-parent):has(.codeblock-customizer-lines-below-collapsed) {
  min-height: unset !important;
}

/* Pre element styling */
.codeblock-customizer-pre.codeblock-customizer-codeblock-collapsed code {
  display: none !important;
}
/* callouts */
.markdown-source-view.mod-cm6 .cm-embed-block pre {
  margin-top: 16px;
}
.markdown-rendered pre.codeblock-customizer-pre {
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
  border-bottom-left-radius: var(--border-radius);
  border-bottom-right-radius: var(--border-radius);
  padding-top: 0px;           /* padding-top and bottom must be set at the pre code element*/
  padding-bottom: 0px;
  padding-right: 0px;         /* originalVal: 16px, disable it, so the lines are fully highlighted */
  padding-left: 0px;          /* originalVal: 16px, disable it, so the lines are fully highlighted */
  min-height: auto;
  margin-top: 16px;           /* originalVal: none, set it to 16px, so the header gets the marginTop, which was removed from the first line of the codeblock */
}
.markdown-rendered pre.codeblock-customizer-pre code {
  display: grid;/*block;*/  /* Fix for highlighting the whole line, when a vertical scrollbar is displayed */
  overflow: auto;           /* Fix for "Copy" button moving when vertical scrollbar is displayed */
  padding-top: var(--code-padding);
  padding-bottom: var(--code-padding);
  padding-left: 0px;
  padding-right: 0px;
  border-radius: 0px;
  background: none !important;
}

/* Source mode text styling */
.HyperMD-codeblock:has(> .cm-widgetBuffer) { /* Prevent Line Wraps */
	white-space: nowrap;
}
.HyperMD-codeblock:has(> .cm-widgetBuffer) > .cm-hmd-codeblock { /* Prevent Line Wraps */
	white-space: break-spaces;
}

/* Reading mode text styling */
/* fix for missing padding on embedded code blocks */
pre.codeblock-customizer-pre [class^=codeblock-customizer-line-number] {
  position: sticky;
}

.markdown-rendered div:has(> .codeblock-customizer-line-text) {
  display: flex;
  /*position: relative;*/
}
.markdown-rendered .codeblock-customizer-line-text {
  flex-basis: 100%;
  padding-left: var(--line-number-gutter-padding);             /* originalVal:0px, add padding between the line numbers and the text */
}

/* Border radius styling */
.codeblock-customizer .markdown-source-view [class*="codeblock-customizer-line"].HyperMD-codeblock-begin {
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
}
.codeblock-customizer .markdown-source-view [class*="codeblock-customizer-line"].HyperMD-codeblock-end/*,
.codeblock-customizer .markdown-source-view [class*="codeblock-customizer-line"].codeblock-customizer-fade-out-line3*/ {
  border-bottom-left-radius: var(--border-radius);
  border-bottom-right-radius: var(--border-radius);
}
.codeblock-customizer .markdown-source-view [class*="codeblock-customizer-line"].HyperMD-codeblock-end .codeblock-customizer-line-number,
.codeblock-customizer .markdown-source-view [class*="codeblock-customizer-line"].HyperMD-codeblock-end .codeblock-customizer-line-number-specific/*,
.codeblock-customizer .markdown-source-view [class*="codeblock-customizer-line"].codeblock-customizer-fade-out-line3 .codeblock-customizer-line-number,
.codeblock-customizer .markdown-source-view [class*="codeblock-customizer-line"].codeblock-customizer-fade-out-line3 .codeblock-customizer-line-number-specific*/ {
  border-bottom-left-radius: var(--border-radius);
}
body:not(.codeblock-customizer-show-langicons-always):not(.codeblock-customizer-show-langnames-always) .markdown-source-view .codeblock-customizer-header-container + .HyperMD-codeblock-begin,
body:not(.codeblock-customizer-show-langnames-always) .markdown-source-view .codeblock-customizer-header-container:not(:has( img.codeblock-customizer-icon)) + .HyperMD-codeblock-begin,
body .markdown-source-view .codeblock-customizer-header-container:not(:has( img.codeblock-customizer-icon)) + .HyperMD-codeblock-begin {
  border-top-left-radius: var(--border-radius);
  border-top-right-radius: var(--border-radius);
}

body:not(.codeblock-customizer-show-langicons-always):not(.codeblock-customizer-show-langnames-always) .codeblock-customizer-header-container + .HyperMD-codeblock-begin .codeblock-customizer-line-number,
body:not(.codeblock-customizer-show-langicons-always):not(.codeblock-customizer-show-langnames-always) .codeblock-customizer-header-container + .HyperMD-codeblock-begin .codeblock-customizer-line-number-specific,
body:not(.codeblock-customizer-show-langnames-always) .codeblock-customizer-header-container:not(:has( img.codeblock-customizer-icon)) + .HyperMD-codeblock-begin .codeblock-customizer-line-number,
body:not(.codeblock-customizer-show-langnames-always) .codeblock-customizer-header-container:not(:has( img.codeblock-customizer-icon)) + .HyperMD-codeblock-begin .codeblock-customizer-line-number-specific,
body .codeblock-customizer-header-container:not(:has( img.codeblock-customizer-icon)) + .HyperMD-codeblock-begin .codeblock-customizer-line-number,
body .codeblock-customizer-header-container:not(:has( img.codeblock-customizer-icon)) + .HyperMD-codeblock-begin .codeblock-customizer-line-number-specific {
  border-top-left-radius: var(--border-radius);
}

body:not(.codeblock-customizer-show-langicons-always):not(.codeblock-customizer-show-langnames-always) .codeblock-customizer-header-container + .codeblock-customizer-pre,
body:not(.codeblock-customizer-show-langnames-always) .codeblock-customizer-header-container:not(:has( img.codeblock-customizer-icon)) + .codeblock-customizer-pre,
body .codeblock-customizer-header-container:not(:has( img.codeblock-customizer-icon)) + .codeblock-customizer-pre {
  border-top-left-radius: var(--border-radius);
  border-top-right-radius: var(--border-radius);
}

body.codeblock-customizer .markdown-source-view :not(pre.codeblock-customizer-pre) > .codeblock-customizer-header-container-specific + [class*=codeblock-customizer-line].HyperMD-codeblock-begin {
  border-top: none !important;
  border-top-left-radius: 0px !important;
  border-top-right-radius: 0px !important;
}

/* Settings page */
.codeblock-customizer-basic-settingsDiv-hide {
  display: none;
}

.codeblock-customizer-codeblock-settingsDiv-hide {
  display: none;
}

.codeblock-customizer-languageSpecific-settingsDiv-hide {
  display: none;
}

.codeblock-customizer-alternative-highlight-settingsDiv-hide {
  display: none;
}

.codeblock-customizer-header-settingsDiv-hide {
  display: none;
}

.codeblock-customizer-header-language-settingsDiv-hide {
  display: none;
}

.codeblock-customizer-gutter-settingsDiv-hide {
  display: none;
}

.codeblock-customizer-inlineCode-settingsDiv-hide {
  display: none;
}

.codeblock-customizer-printToPDF-settingsDiv-hide {
  display: none;
}

/* disabled button */
.clickable-icon.extra-setting-button.is-disabled {
  color: #666666;
  cursor: not-allowed;
  opacity: 0.6;
}

/* Themes */
/* Shimmering focus */
.codeblock-customizer pre.codeblock-customizer-pre::before,
.codeblock-customizer pre.codeblock-customizer-pre::after {
	content: none !important;
}
.codeblock-customizer .markdown-source-view .HyperMD-codeblock[class*='codeblock-customizer-line']::before {
	content: none !important;
}
/* for code blocks without defined language */
.markdown-reading-view pre:not([class^=language-]) {
  overflow-x: auto !important;
  padding: unset !important;
  overflow: hidden; /* vertical scroll bar appears on header */
}

/* Everforest */
/*.codeblock-customizer .markdown-source-view [class*="codeblock-customizer-line"].HyperMD-codeblock-begin,
.codeblock-customizer .markdown-source-view [class*="codeblock-customizer-line"].HyperMD-codeblock-end {
  height: calc(var(--line-height-normal) * 1em);
}*/

/* Settings */
.codeblock-customizer-Donation {
  width: 70%;
  margin: 0 auto;
  text-align: center;
}

.codeblock-customizer-readMe {
  margin-top: 32px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* pickr start */
.pcr-app .pcr-swatches > button {
    padding: 0;
}

/*! Pickr 1.8.2 MIT | https://github.com/Simonwep/pickr */
.pickr {position: relative;overflow: visible;transform: translateY(0)}  .pickr * {box-sizing: border-box;outline: none;border: none;appearance: none;-webkit-appearance: none}  .pickr .pcr-button {position: relative;height: 2em;width: 2em;padding: 0.5em;cursor: pointer;font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;border-radius: .15em;background: url('data:image/svg+xml;utf8, <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 50 50" stroke="%2342445A" stroke-width="5px" stroke-linecap="round"><path d="M45,45L5,5"></path><path d="M45,5L5,45"></path></svg>') no-repeat center;background-size: 0;transition: all 0.3s}  .pickr .pcr-button::before {position: absolute;content: '';top: 0;left: 0;width: 100%;height: 100%;background: url('data:image/svg+xml;utf8, <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 2 2"><path fill="white" d="M1,0H2V1H1V0ZM0,1H1V2H0V1Z"/><path fill="gray" d="M0,0H1V1H0V0ZM1,1H2V2H1V1Z"/></svg>');background-size: .5em;border-radius: .15em;z-index: -1}  .pickr .pcr-button::before {z-index: initial}  .pickr .pcr-button::after {position: absolute;content: '';top: 0;left: 0;height: 100%;width: 100%;transition: background 0.3s;background: var(--pcr-color);border-radius: .15em}  .pickr .pcr-button.clear {background-size: 70%}  .pickr .pcr-button.clear::before {opacity: 0}  .pickr .pcr-button.clear:focus {box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.85), 0 0 0 3px var(--pcr-color)}  .pickr .pcr-button.disabled {cursor: not-allowed}  .pickr *, .pcr-app * {box-sizing: border-box;outline: none;border: none;appearance: none;-webkit-appearance: none}  .pickr input:focus, .pickr input.pcr-active, .pickr button:focus, .pickr button.pcr-active, .pcr-app input:focus, .pcr-app input.pcr-active, .pcr-app button:focus, .pcr-app button.pcr-active {box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.85), 0 0 0 3px var(--pcr-color)}  .pickr .pcr-palette, .pickr .pcr-slider, .pcr-app .pcr-palette, .pcr-app .pcr-slider {transition: box-shadow 0.3s}  .pickr .pcr-palette:focus, .pickr .pcr-slider:focus, .pcr-app .pcr-palette:focus, .pcr-app .pcr-slider:focus {box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.85), 0 0 0 3px rgba(0, 0, 0, 0.25)}  .pcr-app {position: fixed;display: flex;flex-direction: column;z-index: 10000;border-radius: 0.1em;background: #fff;opacity: 0;visibility: hidden;transition: opacity 0.3s, visibility 0s 0.3s;font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;box-shadow: 0 0.15em 1.5em 0 rgba(0, 0, 0, 0.1), 0 0 1em 0 rgba(0, 0, 0, 0.03);left: 0;top: 0}  .pcr-app.visible {transition: opacity 0.3s;visibility: visible;opacity: 1}  .pcr-app .pcr-swatches {display: flex;flex-wrap: wrap;margin-top: 0.75em}  .pcr-app .pcr-swatches.pcr-last {margin: 0}  @supports (display: grid) {  .pcr-app .pcr-swatches {display: grid;align-items: center;grid-template-columns: repeat(auto-fit, 1.75em)}  }  .pcr-app .pcr-swatches > button {font-size: 1em;position: relative;width: calc(1.75em - 10px);height: calc(1.75em - 10px);border-radius: 0.15em;cursor: pointer;margin: 2.5px;flex-shrink: 0;justify-self: center;transition: all 0.15s;overflow: hidden;background: transparent;z-index: 1}  .pcr-app .pcr-swatches > button::before {position: absolute;content: '';top: 0;left: 0;width: 100%;height: 100%;background: url('data:image/svg+xml;utf8, <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 2 2"><path fill="white" d="M1,0H2V1H1V0ZM0,1H1V2H0V1Z"/><path fill="gray" d="M0,0H1V1H0V0ZM1,1H2V2H1V1Z"/></svg>');background-size: 6px;border-radius: .15em;z-index: -1}  .pcr-app .pcr-swatches > button::after {content: '';position: absolute;top: 0;left: 0;width: 100%;height: 100%;background: var(--pcr-color);border: 1px solid rgba(0, 0, 0, 0.05);border-radius: 0.15em;box-sizing: border-box}  .pcr-app .pcr-swatches > button:hover {filter: brightness(1.05)}  .pcr-app .pcr-swatches > button:not(.pcr-active) {box-shadow: none}  .pcr-app .pcr-interaction {display: flex;flex-wrap: wrap;align-items: center;margin: 0 -0.2em 0 -0.2em}  .pcr-app .pcr-interaction > * {margin: 0 0.2em}  .pcr-app .pcr-interaction input {letter-spacing: 0.07em;font-size: 0.75em;text-align: center;cursor: pointer;color: #75797e;background: #f1f3f4;border-radius: .15em;transition: all 0.15s;padding: 0.45em 0.5em;margin-top: 0.75em}  .pcr-app .pcr-interaction input:hover {filter: brightness(0.975)}  .pcr-app .pcr-interaction input:focus {box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.85), 0 0 0 3px rgba(66, 133, 244, 0.75)}  .pcr-app .pcr-interaction .pcr-result {color: #75797e;text-align: left;flex: 1 1 8em;min-width: 8em;transition: all 0.2s;border-radius: .15em;background: #f1f3f4;cursor: text}  .pcr-app .pcr-interaction .pcr-result::-moz-selection {background: #4285f4;color: #fff}  .pcr-app .pcr-interaction .pcr-result::selection {background: #4285f4;color: #fff}  .pcr-app .pcr-interaction .pcr-type.active {color: #fff;background: #4285f4}  .pcr-app .pcr-interaction .pcr-save, .pcr-app .pcr-interaction .pcr-cancel, .pcr-app .pcr-interaction .pcr-clear {color: #fff;width: auto}  .pcr-app .pcr-interaction .pcr-save, .pcr-app .pcr-interaction .pcr-cancel, .pcr-app .pcr-interaction .pcr-clear {color: #fff}  .pcr-app .pcr-interaction .pcr-save:hover, .pcr-app .pcr-interaction .pcr-cancel:hover, .pcr-app .pcr-interaction .pcr-clear:hover {filter: brightness(0.925)}  .pcr-app .pcr-interaction .pcr-save {background: #4285f4}  .pcr-app .pcr-interaction .pcr-clear, .pcr-app .pcr-interaction .pcr-cancel {background: #f44250}  .pcr-app .pcr-interaction .pcr-clear:focus, .pcr-app .pcr-interaction .pcr-cancel:focus {box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.85), 0 0 0 3px rgba(244, 66, 80, 0.75)}  .pcr-app .pcr-selection .pcr-picker {position: absolute;height: 18px;width: 18px;border: 2px solid #fff;border-radius: 100%;-webkit-user-select: none;-moz-user-select: none;-ms-user-select: none;user-select: none}  .pcr-app .pcr-selection .pcr-color-palette, .pcr-app .pcr-selection .pcr-color-chooser, .pcr-app .pcr-selection .pcr-color-opacity {position: relative;-webkit-user-select: none;-moz-user-select: none;-ms-user-select: none;user-select: none;display: flex;flex-direction: column;cursor: grab;cursor: -webkit-grab}  .pcr-app .pcr-selection .pcr-color-palette:active, .pcr-app .pcr-selection .pcr-color-chooser:active, .pcr-app .pcr-selection .pcr-color-opacity:active {cursor: grabbing;cursor: -webkit-grabbing}  .pcr-app[data-theme='nano'] {width: 14.25em;max-width: 95vw}  .pcr-app[data-theme='nano'] .pcr-swatches {margin-top: .6em;padding: 0 .6em}  .pcr-app[data-theme='nano'] .pcr-interaction {padding: 0 .6em .6em .6em}  .pcr-app[data-theme='nano'] .pcr-selection {display: grid;grid-gap: .6em;grid-template-columns: 1fr 4fr;grid-template-rows: 5fr auto auto;align-items: center;height: 10.5em;width: 100%;align-self: flex-start}  .pcr-app[data-theme='nano'] .pcr-selection .pcr-color-preview {grid-area: 2 / 1 / 4 / 1;height: 100%;width: 100%;display: flex;flex-direction: row;justify-content: center;margin-left: .6em}  .pcr-app[data-theme='nano'] .pcr-selection .pcr-color-preview .pcr-last-color {display: none}  .pcr-app[data-theme='nano'] .pcr-selection .pcr-color-preview .pcr-current-color {position: relative;background: var(--pcr-color);width: 2em;height: 2em;border-radius: 50em;overflow: hidden}  .pcr-app[data-theme='nano'] .pcr-selection .pcr-color-preview .pcr-current-color::before {position: absolute;content: '';top: 0;left: 0;width: 100%;height: 100%;background: url('data:image/svg+xml;utf8, <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 2 2"><path fill="white" d="M1,0H2V1H1V0ZM0,1H1V2H0V1Z"/><path fill="gray" d="M0,0H1V1H0V0ZM1,1H2V2H1V1Z"/></svg>');background-size: .5em;border-radius: .15em;z-index: -1}  .pcr-app[data-theme='nano'] .pcr-selection .pcr-color-palette {grid-area: 1 / 1 / 2 / 3;width: 100%;height: 100%;z-index: 1}  .pcr-app[data-theme='nano'] .pcr-selection .pcr-color-palette .pcr-palette {border-radius: .15em;width: 100%;height: 100%}  .pcr-app[data-theme='nano'] .pcr-selection .pcr-color-palette .pcr-palette::before {position: absolute;content: '';top: 0;left: 0;width: 100%;height: 100%;background: url('data:image/svg+xml;utf8, <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 2 2"><path fill="white" d="M1,0H2V1H1V0ZM0,1H1V2H0V1Z"/><path fill="gray" d="M0,0H1V1H0V0ZM1,1H2V2H1V1Z"/></svg>');background-size: .5em;border-radius: .15em;z-index: -1}  .pcr-app[data-theme='nano'] .pcr-selection .pcr-color-chooser {grid-area: 2 / 2 / 2 / 2}  .pcr-app[data-theme='nano'] .pcr-selection .pcr-color-opacity {grid-area: 3 / 2 / 3 / 2}  .pcr-app[data-theme='nano'] .pcr-selection .pcr-color-chooser, .pcr-app[data-theme='nano'] .pcr-selection .pcr-color-opacity {height: 0.5em;margin: 0 .6em}  .pcr-app[data-theme='nano'] .pcr-selection .pcr-color-chooser .pcr-picker, .pcr-app[data-theme='nano'] .pcr-selection .pcr-color-opacity .pcr-picker {top: 50%;transform: translateY(-50%)}  .pcr-app[data-theme='nano'] .pcr-selection .pcr-color-chooser .pcr-slider, .pcr-app[data-theme='nano'] .pcr-selection .pcr-color-opacity .pcr-slider {flex-grow: 1;border-radius: 50em}  .pcr-app[data-theme='nano'] .pcr-selection .pcr-color-chooser .pcr-slider {background: linear-gradient(to right, red, #ff0, lime, cyan, blue, #f0f, red)}  .pcr-app[data-theme='nano'] .pcr-selection .pcr-color-opacity .pcr-slider {background: linear-gradient(to right, transparent, black), url('data:image/svg+xml;utf8, <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 2 2"><path fill="white" d="M1,0H2V1H1V0ZM0,1H1V2H0V1Z"/><path fill="gray" d="M0,0H1V1H0V0ZM1,1H2V2H1V1Z"/></svg>');background-size: 100%, 0.25em}
/* pickr end */