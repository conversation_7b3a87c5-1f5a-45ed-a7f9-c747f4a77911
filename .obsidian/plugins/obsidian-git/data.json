{"commitMessage": "{{hostname}}笔记更新时间: {{date}}", "commitDateFormat": "YYYY-MM-DD HH:mm:ss", "autoSaveInterval": 1440, "autoPushInterval": 1441, "autoPullInterval": 180, "autoPullOnBoot": false, "disablePush": false, "pullBeforePush": true, "disablePopups": false, "disablePopupsForNoChanges": false, "listChangedFilesInMessageBody": true, "showStatusBar": true, "updateSubmodules": false, "syncMethod": "merge", "customMessageOnAutoBackup": true, "autoBackupAfterFileChange": true, "treeStructure": true, "refreshSourceControl": true, "basePath": "", "differentIntervalCommitAndPush": true, "changedFilesInStatusBar": false, "showedMobileNotice": true, "refreshSourceControlTimer": 7000, "showBranchStatusBar": true, "setLastSaveToLastCommit": false, "submoduleRecurseCheckout": false, "gitDir": "", "showFileMenu": true, "authorInHistoryView": "full", "dateInHistoryView": true, "diffStyle": "split", "lineAuthor": {"show": false, "followMovement": "inactive", "authorDisplay": "initials", "showCommitHash": false, "dateTimeFormatOptions": "date", "dateTimeFormatCustomString": "YYYY-MM-DD HH:mm", "dateTimeTimezone": "viewer-local", "coloringMaxAge": "1y", "colorNew": {"r": 255, "g": 150, "b": 150}, "colorOld": {"r": 120, "g": 160, "b": 255}, "textColorCss": "var(--text-muted)", "ignoreWhitespace": false, "gutterSpacingFallbackLength": 12, "lastShownAuthorDisplay": "initials", "lastShownDateTimeFormatOptions": "date"}, "autoCommitMessage": "笔记更新: {{date}}"}