{"Tabout": true, "SelectionEnhance": true, "IntrinsicSymbolPairs": true, "BaseObEditEnhance": true, "FW2HWEnhance": true, "BetterCodeEdit": true, "BetterBackspace": true, "AutoFormat": true, "ExcludeFiles": "", "ChineseEnglishSpace": false, "ChineseNumberSpace": false, "EnglishNumberSpace": false, "ChineseNoSpace": false, "QuoteSpace": true, "PunctuationSpace": true, "AutoCapital": false, "AutoCapitalMode": "typing", "PunctuationSpaceMode": "typing", "InlineCodeSpaceMode": 1, "InlineFormulaSpaceMode": 1, "InlineLinkSpaceMode": 1, "InlineLinkSmartSpace": true, "UserDefinedRegSwitch": true, "UserDefinedRegExp": "{{.*?}}|++\n<.*?>|--\n\\[\\!.*?\\][-+]{0,1}|-+\n(file:///|https?://|ftp://|obsidian://|zotero://|www.)[^\\s（）《》。,，！？;；：“”‘’\\)\\(\\[\\]\\{\\}']+|--\n\n[a-zA-Z0-9_\\-.]+@[a-zA-Z0-9_\\-.]+|++\n(?<!#)#[\\u4e00-\\u9fa5\\w-\\/]+|++", "debug": false, "userSelRepRuleTrigger": ["-", "#"], "userSelRepRuleValue": [{"left": "~~", "right": "~~"}, {"left": "#", "right": " "}], "userDeleteRulesStrList": [["demo|", "|"]], "userConvertRulesStrList": [[":)|", "😀|"]], "userSelRuleSettingsOpen": true, "userDelRuleSettingsOpen": true, "userCvtRuleSettingsOpen": true, "StrictModeEnter": false, "StrictLineMode": "enter_twice", "EnhanceModA": false, "TryFixChineseIM": true, "PuncRectify": false, "FixMacOSContextMenu": false, "TryFixMSIME": false, "EnterTwice": false}